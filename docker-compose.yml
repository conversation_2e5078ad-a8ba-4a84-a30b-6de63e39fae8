version: '3.8'

services:
  # Base de datos PostgreSQL
  database:
    image: postgres:15-alpine
    container_name: pautas-db
    environment:
      POSTGRES_DB: pautas_publicitarias
      POSTGRES_USER: pautas_user
      POSTGRES_PASSWORD: pautas_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - pautas-network

  # Redis para cache y sesiones
  redis:
    image: redis:7-alpine
    container_name: pautas-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - pautas-network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: pautas-backend
    environment:
      NODE_ENV: development
      DATABASE_URL: ******************************************************/pautas_publicitarias
      REDIS_URL: redis://redis:6379
      JWT_SECRET: your-super-secret-jwt-key-change-in-production
      VMIX_API_URL: http://host.docker.internal:8088
      DINESAT_OUTPUT_PATH: /app/dinesat-output
    ports:
      - "3001:3001"
    volumes:
      - ./backend:/app
      - /app/node_modules
      - dinesat_files:/app/dinesat-output
      - media_files:/app/media
    depends_on:
      - database
      - redis
    networks:
      - pautas-network
    restart: unless-stopped

  # Frontend React
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: pautas-frontend
    environment:
      VITE_API_URL: http://localhost:3001
      VITE_WS_URL: ws://localhost:3001
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - pautas-network
    restart: unless-stopped

  # Nginx reverse proxy
  nginx:
    image: nginx:alpine
    container_name: pautas-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - pautas-network
    restart: unless-stopped

  # Prometheus para métricas
  prometheus:
    image: prom/prometheus:latest
    container_name: pautas-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - pautas-network

  # Grafana para dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: pautas-grafana
    ports:
      - "3002:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - pautas-network

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:
  dinesat_files:
  media_files:

networks:
  pautas-network:
    driver: bridge
