import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { createServer } from 'http';
import { Server } from 'socket.io';
import dotenv from 'dotenv';

import { logger } from '@/utils/logger';
import { errorHandler } from '@/middleware/errorHandler';
import { authMiddleware } from '@/middleware/auth';
import { validateRequest } from '@/middleware/validation';

// Routes
import authRoutes from '@/routes/auth';
import clientRoutes from '@/routes/clients';
import advertisementRoutes from '@/routes/advertisements';
import scheduleRoutes from '@/routes/schedules';
import reportRoutes from '@/routes/reports';
import systemRoutes from '@/routes/system';

// Services
import { SchedulerService } from '@/services/SchedulerService';
import { VMixService } from '@/services/VMixService';
import { DinesatService } from '@/services/DinesatService';
import { WebSocketService } from '@/services/WebSocketService';

// Load environment variables
dotenv.config();

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: process.env.FRONTEND_URL || "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});

const PORT = process.env.PORT || 3001;

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});

// Middleware
app.use(helmet());
app.use(cors({
  origin: process.env.FRONTEND_URL || "http://localhost:3000",
  credentials: true
}));
app.use(compression());
app.use(limiter);
app.use(morgan('combined', { stream: { write: message => logger.info(message.trim()) } }));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// API Routes
app.use('/api/auth', authRoutes);
app.use('/api/clients', authMiddleware, clientRoutes);
app.use('/api/advertisements', authMiddleware, advertisementRoutes);
app.use('/api/schedules', authMiddleware, scheduleRoutes);
app.use('/api/reports', authMiddleware, reportRoutes);
app.use('/api/system', authMiddleware, systemRoutes);

// WebSocket setup
const webSocketService = new WebSocketService(io);

// Initialize services
const vmixService = new VMixService({
  apiUrl: process.env.VMIX_API_URL || 'http://localhost:8088',
  timeout: parseInt(process.env.VMIX_TIMEOUT || '5000'),
  retryAttempts: parseInt(process.env.VMIX_RETRY_ATTEMPTS || '3')
});

const dinesatService = new DinesatService({
  outputPath: process.env.DINESAT_OUTPUT_PATH || './dinesat-output',
  format: 'M3U',
  watchDirectory: true
});

const schedulerService = new SchedulerService(vmixService, dinesatService, webSocketService);

// Error handling
app.use(errorHandler);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: `Route ${req.originalUrl} not found`
  });
});

// Graceful shutdown
process.on('SIGTERM', async () => {
  logger.info('SIGTERM received, shutting down gracefully');
  
  // Stop scheduler
  schedulerService.stop();
  
  // Close server
  server.close(() => {
    logger.info('Process terminated');
    process.exit(0);
  });
});

process.on('SIGINT', async () => {
  logger.info('SIGINT received, shutting down gracefully');
  
  // Stop scheduler
  schedulerService.stop();
  
  // Close server
  server.close(() => {
    logger.info('Process terminated');
    process.exit(0);
  });
});

// Start server
server.listen(PORT, () => {
  logger.info(`🚀 Server running on port ${PORT}`);
  logger.info(`📊 Health check available at http://localhost:${PORT}/health`);
  logger.info(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
  
  // Start scheduler service
  schedulerService.start();
  
  logger.info('✅ All services initialized successfully');
});

export default app;
