anti_patterns:
  - name: "Interfaz de Múltiples Ventanas"
    description: "Sistemas que requieren abrir múltiples ventanas para completar tareas básicas"
    rationale: "Fragmenta la experiencia del usuario y aumenta la complejidad cognitiva"
    evidence:
      - source: "WideOrbit user feedback"
        snippet: "Necesito tener 5 ventanas abiertas solo para programar un anuncio"
    do_instead: "Dashboard unificado con navegación contextual y modales para tareas específicas"

  - name: "Grillas de Datos Densas"
    description: "Tablas con muchas columnas y filas pequeñas que requieren scroll horizontal"
    rationale: "Dificulta la lectura y navegación, especialmente en pantallas pequeñas"
    evidence:
      - source: "DINESAT interface analysis"
        snippet: "Tabla de programación con 15+ columnas visibles simultáneamente"
    do_instead: "Vista de tarjetas o timeline visual con información jerárquica"

  - name: "Formularios Extensos sin Agrupación"
    description: "Formularios largos con todos los campos visibles sin organización lógica"
    rationale: "Abruma al usuario y aumenta la probabilidad de errores"
    evidence:
      - source: "HARDATA setup process"
        snippet: "Formulario de configuración con 50+ campos en una sola página"
    do_instead: "Wizard paso a paso con agrupación lógica y validación progresiva"

  - name: "Colores Genéricos de Sistema"
    description: "Uso de colores por defecto del sistema operativo (#0078d4, #28a745, etc.)"
    rationale: "No diferencia la aplicación y se ve genérica"
    evidence:
      - source: "PlayoutONE screenshots"
        snippet: "Botones azules estándar de Windows"
    do_instead: "Paleta de colores personalizada que refleje la identidad de broadcast"

  - name: "Iconografía Genérica"
    description: "Uso de iconos estándar de sistema sin personalización"
    rationale: "No comunica el contexto específico de broadcast"
    evidence:
      - source: "Multiple broadcast software"
        snippet: "Iconos genéricos de carpeta, documento, configuración"
    do_instead: "Iconografía personalizada que represente conceptos de broadcast"

  - name: "Navegación por Menús Profundos"
    description: "Estructuras de menú con más de 3 niveles de profundidad"
    rationale: "Los usuarios se pierden y no pueden encontrar funciones rápidamente"
    evidence:
      - source: "WideOrbit navigation analysis"
        snippet: "Función de reportes ubicada en Menú > Herramientas > Reportes > Avanzados > Proof of Play"
    do_instead: "Navegación plana con búsqueda global y accesos directos contextuales"

  - name: "Terminología Técnica sin Contexto"
    description: "Uso de jerga técnica sin explicaciones o ayuda contextual"
    rationale: "Excluye a usuarios no técnicos y aumenta la curva de aprendizaje"
    evidence:
      - source: "DINESAT user manual"
        snippet: "Configurar ASIO buffer size para optimización de latencia"
    do_instead: "Lenguaje claro con tooltips explicativos y ayuda contextual"

  - name: "Estados de Error sin Acción Clara"
    description: "Mensajes de error técnicos sin indicar qué hacer para resolverlos"
    rationale: "Frustra a los usuarios y genera llamadas de soporte innecesarias"
    evidence:
      - source: "vMix integration errors"
        snippet: "Error 404: Endpoint not found"
    do_instead: "Mensajes de error claros con pasos específicos de resolución"

  - name: "Configuración Compleja Inicial"
    description: "Procesos de setup que requieren conocimiento técnico avanzado"
    rationale: "Barrera de entrada alta que impide adopción rápida"
    evidence:
      - source: "HARDATA installation guide"
        snippet: "Configurar 15 parámetros de red antes del primer uso"
    do_instead: "Setup automático con configuración inteligente por defecto"

  - name: "Reportes Solo en Formatos Legacy"
    description: "Exportación únicamente a Excel o PDF sin opciones modernas"
    rationale: "Limita la integración con sistemas modernos y workflows digitales"
    evidence:
      - source: "Multiple systems analysis"
        snippet: "Exportar solo a .xls o .pdf"
    do_instead: "Múltiples formatos incluyendo JSON, CSV, y APIs para integración"

  - name: "Interfaz No Responsiva"
    description: "Diseños que no se adaptan a diferentes tamaños de pantalla"
    rationale: "Limita el acceso móvil y la flexibilidad de uso"
    evidence:
      - source: "Desktop-only applications"
        snippet: "Interfaz fija diseñada solo para monitores 1920x1080"
    do_instead: "Diseño responsivo que funciona en desktop, tablet y móvil"

  - name: "Falta de Feedback Visual"
    description: "Acciones sin confirmación visual o indicadores de progreso"
    rationale: "Los usuarios no saben si sus acciones fueron exitosas"
    evidence:
      - source: "DINESAT playlist updates"
        snippet: "Guardar playlist sin confirmación visual"
    do_instead: "Feedback inmediato con animaciones, notificaciones y estados claros"
