# Ejemplos de Integración vMix - Comandos API Detallados

## Comandos Básicos de vMix API

### 1. Reproducir Anuncio de Video

#### Comando HTTP
```http
GET http://localhost:8088/api/?Function=StartInput&Input=C:\Videos\Anuncios\cliente_abc_verano.mp4
```

#### Implementación en TypeScript
```typescript
async function playAdvertisement(filePath: string): Promise<VMixResponse> {
  const url = `${VMIX_BASE_URL}/api/?Function=StartInput&Input=${encodeURIComponent(filePath)}`;
  
  try {
    const response = await fetch(url, {
      method: 'GET',
      timeout: 5000
    });
    
    if (!response.ok) {
      throw new Error(`vMix API error: ${response.status}`);
    }
    
    return { success: true, timestamp: new Date() };
  } catch (error) {
    throw new VMixError(`Failed to play advertisement: ${error.message}`);
  }
}
```

### 2. Pre-cargar Input de Video

#### Comando HTTP
```http
GET http://localhost:8088/api/?Function=AddInput&Value=Video|C:\Videos\Anuncios\cliente_xyz.mp4&Input=5
```

#### Implementación
```typescript
async function preloadInput(filePath: string, inputNumber: number): Promise<void> {
  const params = new URLSearchParams({
    Function: 'AddInput',
    Value: `Video|${filePath}`,
    Input: inputNumber.toString()
  });
  
  const url = `${VMIX_BASE_URL}/api/?${params}`;
  await fetch(url);
}
```

### 3. Cambiar a Input Específico (Cut)

#### Comando HTTP
```http
GET http://localhost:8088/api/?Function=Cut&Input=5
```

#### Implementación
```typescript
async function cutToInput(inputNumber: number): Promise<void> {
  const url = `${VMIX_BASE_URL}/api/?Function=Cut&Input=${inputNumber}`;
  await fetch(url);
}
```

### 4. Overlay de Anuncio

#### Comando HTTP
```http
GET http://localhost:8088/api/?Function=OverlayInput1&Input=C:\Videos\Overlays\logo_cliente.png
```

#### Implementación
```typescript
async function showOverlay(overlayPath: string, overlayNumber: number = 1): Promise<void> {
  const url = `${VMIX_BASE_URL}/api/?Function=OverlayInput${overlayNumber}&Input=${encodeURIComponent(overlayPath)}`;
  await fetch(url);
}
```

### 5. Consultar Estado del Sistema

#### Comando HTTP
```http
GET http://localhost:8088/api/
```

#### Respuesta XML Ejemplo
```xml
<vmix>
  <version>*********</version>
  <edition>Pro</edition>
  <inputs>
    <input key="1" number="1" type="Video" title="anuncio_cliente1.mp4" state="Running" position="5432" duration="30000" loop="False">
      C:\Videos\Anuncios\anuncio_cliente1.mp4
    </input>
    <input key="2" number="2" type="Video" title="anuncio_cliente2.mp4" state="Paused" position="0" duration="15000" loop="False">
      C:\Videos\Anuncios\anuncio_cliente2.mp4
    </input>
  </inputs>
  <overlays>
    <overlay number="1">2</overlay>
  </overlays>
  <preview>1</preview>
  <active>1</active>
</vmix>
```

#### Parser de Estado
```typescript
interface VMixStatus {
  version: string;
  edition: string;
  inputs: VMixInput[];
  activeInput: number;
  previewInput: number;
  overlays: VMixOverlay[];
}

interface VMixInput {
  key: string;
  number: number;
  type: string;
  title: string;
  state: 'Running' | 'Paused' | 'Completed';
  position: number;
  duration: number;
  filePath: string;
}

function parseVMixStatus(xmlString: string): VMixStatus {
  const parser = new DOMParser();
  const doc = parser.parseFromString(xmlString, 'text/xml');
  
  const inputs: VMixInput[] = [];
  const inputElements = doc.querySelectorAll('input');
  
  inputElements.forEach(input => {
    inputs.push({
      key: input.getAttribute('key') || '',
      number: parseInt(input.getAttribute('number') || '0'),
      type: input.getAttribute('type') || '',
      title: input.getAttribute('title') || '',
      state: input.getAttribute('state') as any || 'Paused',
      position: parseInt(input.getAttribute('position') || '0'),
      duration: parseInt(input.getAttribute('duration') || '0'),
      filePath: input.textContent || ''
    });
  });
  
  return {
    version: doc.querySelector('version')?.textContent || '',
    edition: doc.querySelector('edition')?.textContent || '',
    inputs,
    activeInput: parseInt(doc.querySelector('active')?.textContent || '0'),
    previewInput: parseInt(doc.querySelector('preview')?.textContent || '0'),
    overlays: []
  };
}
```

## Secuencias de Comandos Complejas

### 1. Secuencia de Anuncio Completa

```typescript
async function executeAdvertisementSequence(ad: Advertisement): Promise<void> {
  try {
    // 1. Pre-cargar el video en un input libre
    const freeInput = await findFreeInput();
    await preloadInput(ad.videoFilePath, freeInput);
    
    // 2. Esperar a que esté listo
    await waitForInputReady(freeInput);
    
    // 3. En el momento exacto, hacer cut al anuncio
    await cutToInput(freeInput);
    
    // 4. Opcional: Mostrar overlay con información
    if (ad.overlayPath) {
      await showOverlay(ad.overlayPath);
    }
    
    // 5. Esperar a que termine el anuncio
    await waitForInputCompletion(freeInput, ad.duration);
    
    // 6. Volver al input principal
    await cutToInput(1); // Input principal
    
    // 7. Limpiar el input usado
    await removeInput(freeInput);
    
  } catch (error) {
    throw new Error(`Advertisement sequence failed: ${error.message}`);
  }
}
```

### 2. Monitoreo Continuo de Estado

```typescript
class VMixMonitor {
  private intervalId: NodeJS.Timeout | null = null;
  private lastStatus: VMixStatus | null = null;
  
  startMonitoring(intervalMs: number = 1000): void {
    this.intervalId = setInterval(async () => {
      try {
        const currentStatus = await this.getSystemStatus();
        
        if (this.hasStatusChanged(currentStatus)) {
          this.handleStatusChange(currentStatus);
          this.lastStatus = currentStatus;
        }
      } catch (error) {
        console.error('vMix monitoring error:', error);
      }
    }, intervalMs);
  }
  
  stopMonitoring(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }
  
  private hasStatusChanged(newStatus: VMixStatus): boolean {
    if (!this.lastStatus) return true;
    
    return (
      this.lastStatus.activeInput !== newStatus.activeInput ||
      this.lastStatus.inputs.length !== newStatus.inputs.length
    );
  }
  
  private handleStatusChange(status: VMixStatus): void {
    // Notificar cambios de estado al sistema principal
    EventBus.emit('vmix:status:changed', status);
  }
}
```

## Manejo de Errores Específicos

### Códigos de Error Comunes

```typescript
enum VMixErrorCode {
  CONNECTION_REFUSED = 'ECONNREFUSED',
  TIMEOUT = 'ETIMEDOUT',
  INPUT_NOT_FOUND = 'INPUT_NOT_FOUND',
  INVALID_FUNCTION = 'INVALID_FUNCTION',
  FILE_NOT_FOUND = 'FILE_NOT_FOUND'
}

class VMixErrorHandler {
  static handle(error: any): VMixError {
    switch (error.code) {
      case VMixErrorCode.CONNECTION_REFUSED:
        return new VMixError(
          'vMix no está ejecutándose o no es accesible en el puerto 8088',
          VMixErrorCode.CONNECTION_REFUSED,
          'Verificar que vMix esté abierto y la API Web esté habilitada'
        );
        
      case VMixErrorCode.TIMEOUT:
        return new VMixError(
          'Timeout al conectar con vMix',
          VMixErrorCode.TIMEOUT,
          'Verificar la conexión de red y el estado de vMix'
        );
        
      case VMixErrorCode.FILE_NOT_FOUND:
        return new VMixError(
          'Archivo de video no encontrado',
          VMixErrorCode.FILE_NOT_FOUND,
          'Verificar que el archivo existe y la ruta es correcta'
        );
        
      default:
        return new VMixError(
          `Error desconocido: ${error.message}`,
          'UNKNOWN',
          'Contactar soporte técnico'
        );
    }
  }
}
```

## Configuración y Setup

### Configuración de vMix para API

1. **Habilitar Web Controller**:
   - Settings → Web Controller → Enable
   - Port: 8088 (default)
   - Password: (opcional pero recomendado)

2. **Configurar Inputs**:
   - Reservar inputs específicos para anuncios (ej: 10-20)
   - Configurar rutas de archivos accesibles

3. **Network Settings**:
   - Permitir conexiones desde la IP del servidor
   - Configurar firewall si es necesario

### Configuración del Sistema

```typescript
interface VMixConfig {
  apiUrl: string;
  apiPort: number;
  password?: string;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
  monitoringInterval: number;
  reservedInputs: {
    start: number;
    end: number;
  };
}

const defaultVMixConfig: VMixConfig = {
  apiUrl: 'http://localhost',
  apiPort: 8088,
  timeout: 5000,
  retryAttempts: 3,
  retryDelay: 1000,
  monitoringInterval: 1000,
  reservedInputs: {
    start: 10,
    end: 20
  }
};
```
