/** @type {import('tailwindcss').Config} */
export default {
  darkMode: ["class"],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
  ],
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        // Broadcast color palette
        broadcast: {
          red: '#E53E3E',      // ON AIR red
          green: '#38A169',    // READY green
          amber: '#D69E2E',    // WARNING amber
          blue: '#3182CE',     // INFO blue
        },
        studio: {
          black: '#1A202C',    // Studio black
          gray: '#2D3748',     // Console gray
          panel: '#4A5568',    // Panel gray
          light: '#E2E8F0',    // Light text
        },
        frequency: {
          purple: '#805AD5',   // Frequency purple
          cyan: '#00B5D8',     // Signal cyan
        },
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: 0 },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: 0 },
        },
        pulse: {
          '0%, 100%': { opacity: 1 },
          '50%': { opacity: 0.5 },
        },
        'fade-in': {
          '0%': { opacity: 0, transform: 'translateY(10px)' },
          '100%': { opacity: 1, transform: 'translateY(0)' },
        },
        'slide-in': {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(0)' },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        pulse: 'pulse 2s infinite',
        'fade-in': 'fade-in 0.3s ease-out',
        'slide-in': 'slide-in 0.3s ease-out',
      },
      fontFamily: {
        'broadcast': ['Inter', 'system-ui', 'sans-serif'],
        'mono': ['JetBrains Mono', 'Consolas', 'monospace'],
      },
      fontSize: {
        'broadcast-xl': ['1.5rem', { lineHeight: '1.2', fontWeight: '600' }],
        'broadcast-lg': ['1.25rem', { lineHeight: '1.3', fontWeight: '500' }],
        'broadcast-md': ['1rem', { lineHeight: '1.4', fontWeight: '400' }],
        'broadcast-sm': ['0.875rem', { lineHeight: '1.4', fontWeight: '400' }],
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      },
      screens: {
        'broadcast': '1920px', // Studio monitors
        'production': '1440px', // Production laptops
        'mobile-broadcast': '768px', // Tablets for mobile operators
      },
      boxShadow: {
        'broadcast': '0 4px 12px rgba(0, 0, 0, 0.15)',
        'studio': '0 8px 24px rgba(26, 32, 44, 0.3)',
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
}
