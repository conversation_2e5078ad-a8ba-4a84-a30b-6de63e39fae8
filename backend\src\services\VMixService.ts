import axios, { AxiosInstance } from 'axios';
import { logger } from '@/utils/logger';

export interface VMixConfig {
  apiUrl: string;
  timeout: number;
  retryAttempts: number;
  password?: string;
}

export interface VMixResponse {
  success: boolean;
  timestamp: Date;
  data?: any;
  error?: string;
}

export interface VMixStatus {
  version: string;
  edition: string;
  inputs: VMixInput[];
  activeInput: number;
  previewInput: number;
  overlays: VMixOverlay[];
}

export interface VMixInput {
  key: string;
  number: number;
  type: string;
  title: string;
  state: 'Running' | 'Paused' | 'Completed';
  position: number;
  duration: number;
  filePath: string;
}

export interface VMixOverlay {
  number: number;
  input: number;
}

export class VMixError extends Error {
  constructor(
    message: string,
    public code: string = 'UNKNOWN',
    public suggestion?: string
  ) {
    super(message);
    this.name = 'VMixError';
  }
}

export class VMixService {
  private client: AxiosInstance;
  private config: VMixConfig;

  constructor(config: VMixConfig) {
    this.config = config;
    this.client = axios.create({
      baseURL: config.apiUrl,
      timeout: config.timeout,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    // Add request interceptor for logging
    this.client.interceptors.request.use(
      (config) => {
        logger.debug(`vMix API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        logger.error('vMix API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Add response interceptor for logging
    this.client.interceptors.response.use(
      (response) => {
        logger.debug(`vMix API Response: ${response.status} ${response.statusText}`);
        return response;
      },
      (error) => {
        logger.error('vMix API Response Error:', error.message);
        return Promise.reject(this.handleError(error));
      }
    );
  }

  /**
   * Play advertisement video
   */
  async playAdvertisement(filePath: string, inputNumber?: number): Promise<VMixResponse> {
    try {
      const params = new URLSearchParams({
        Function: 'StartInput',
        Input: filePath
      });

      if (inputNumber) {
        params.append('Value', inputNumber.toString());
      }

      const response = await this.client.get(`/api/?${params}`);
      
      return {
        success: true,
        timestamp: new Date(),
        data: response.data
      };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Stop input playback
   */
  async stopInput(filePath: string): Promise<VMixResponse> {
    try {
      const params = new URLSearchParams({
        Function: 'StopInput',
        Input: filePath
      });

      const response = await this.client.get(`/api/?${params}`);
      
      return {
        success: true,
        timestamp: new Date(),
        data: response.data
      };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Cut to specific input
   */
  async cutToInput(inputNumber: number): Promise<VMixResponse> {
    try {
      const params = new URLSearchParams({
        Function: 'Cut',
        Input: inputNumber.toString()
      });

      const response = await this.client.get(`/api/?${params}`);
      
      return {
        success: true,
        timestamp: new Date(),
        data: response.data
      };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Preload input
   */
  async preloadInput(filePath: string, inputNumber: number): Promise<VMixResponse> {
    try {
      const params = new URLSearchParams({
        Function: 'AddInput',
        Value: `Video|${filePath}`,
        Input: inputNumber.toString()
      });

      const response = await this.client.get(`/api/?${params}`);
      
      return {
        success: true,
        timestamp: new Date(),
        data: response.data
      };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Show overlay
   */
  async showOverlay(overlayPath: string, overlayNumber: number = 1): Promise<VMixResponse> {
    try {
      const params = new URLSearchParams({
        Function: `OverlayInput${overlayNumber}`,
        Input: overlayPath
      });

      const response = await this.client.get(`/api/?${params}`);
      
      return {
        success: true,
        timestamp: new Date(),
        data: response.data
      };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Get system status
   */
  async getSystemStatus(): Promise<VMixStatus> {
    try {
      const response = await this.client.get('/api/');
      return this.parseXMLStatus(response.data);
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Check if vMix is online
   */
  async isOnline(): Promise<boolean> {
    try {
      await this.client.get('/api/', { timeout: 2000 });
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Find free input number
   */
  async findFreeInput(startFrom: number = 10, endAt: number = 20): Promise<number> {
    try {
      const status = await this.getSystemStatus();
      const usedInputs = status.inputs.map(input => input.number);
      
      for (let i = startFrom; i <= endAt; i++) {
        if (!usedInputs.includes(i)) {
          return i;
        }
      }
      
      throw new VMixError('No free inputs available', 'NO_FREE_INPUTS');
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Execute advertisement sequence
   */
  async executeAdvertisementSequence(
    filePath: string,
    duration: number,
    overlayPath?: string
  ): Promise<VMixResponse> {
    try {
      // 1. Find free input
      const freeInput = await this.findFreeInput();
      
      // 2. Preload video
      await this.preloadInput(filePath, freeInput);
      
      // 3. Wait a moment for loading
      await this.delay(500);
      
      // 4. Cut to advertisement
      await this.cutToInput(freeInput);
      
      // 5. Show overlay if provided
      if (overlayPath) {
        await this.showOverlay(overlayPath);
      }
      
      // 6. Wait for advertisement duration
      await this.delay(duration * 1000);
      
      // 7. Cut back to main input (assuming input 1)
      await this.cutToInput(1);
      
      // 8. Remove the used input
      await this.removeInput(freeInput);
      
      return {
        success: true,
        timestamp: new Date(),
        data: { inputUsed: freeInput, duration }
      };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Remove input
   */
  private async removeInput(inputNumber: number): Promise<void> {
    const params = new URLSearchParams({
      Function: 'RemoveInput',
      Input: inputNumber.toString()
    });

    await this.client.get(`/api/?${params}`);
  }

  /**
   * Parse XML status response
   */
  private parseXMLStatus(xmlString: string): VMixStatus {
    // Simple XML parsing - in production, use a proper XML parser
    const inputs: VMixInput[] = [];
    
    // Extract basic info (simplified parsing)
    const versionMatch = xmlString.match(/<version>(.*?)<\/version>/);
    const editionMatch = xmlString.match(/<edition>(.*?)<\/edition>/);
    const activeMatch = xmlString.match(/<active>(.*?)<\/active>/);
    const previewMatch = xmlString.match(/<preview>(.*?)<\/preview>/);
    
    return {
      version: versionMatch?.[1] || 'Unknown',
      edition: editionMatch?.[1] || 'Unknown',
      inputs,
      activeInput: parseInt(activeMatch?.[1] || '0'),
      previewInput: parseInt(previewMatch?.[1] || '0'),
      overlays: []
    };
  }

  /**
   * Handle and transform errors
   */
  private handleError(error: any): VMixError {
    if (error.code === 'ECONNREFUSED') {
      return new VMixError(
        'vMix no está ejecutándose o no es accesible',
        'CONNECTION_REFUSED',
        'Verificar que vMix esté abierto y la API Web esté habilitada'
      );
    }
    
    if (error.code === 'ETIMEDOUT') {
      return new VMixError(
        'Timeout al conectar con vMix',
        'TIMEOUT',
        'Verificar la conexión de red y el estado de vMix'
      );
    }
    
    if (error.response?.status === 404) {
      return new VMixError(
        'Archivo de video no encontrado',
        'FILE_NOT_FOUND',
        'Verificar que el archivo existe y la ruta es correcta'
      );
    }
    
    return new VMixError(
      `Error de vMix: ${error.message}`,
      'UNKNOWN',
      'Contactar soporte técnico'
    );
  }

  /**
   * Utility delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
