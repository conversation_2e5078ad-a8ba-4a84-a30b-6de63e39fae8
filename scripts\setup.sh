#!/bin/bash

# Setup script para Sistema de Gestión de Pautas Publicitarias
# Este script configura el entorno de desarrollo completo

set -e

echo "🚀 Configurando Sistema de Gestión de Pautas Publicitarias..."

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Función para imprimir mensajes coloreados
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Verificar prerrequisitos
check_prerequisites() {
    print_status "Verificando prerrequisitos..."
    
    # Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js no está instalado. Por favor instalar Node.js 18+"
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        print_error "Node.js versión 18+ requerida. Versión actual: $(node -v)"
        exit 1
    fi
    
    # npm
    if ! command -v npm &> /dev/null; then
        print_error "npm no está instalado"
        exit 1
    fi
    
    # Docker
    if ! command -v docker &> /dev/null; then
        print_warning "Docker no está instalado. Algunas funcionalidades no estarán disponibles."
    fi
    
    # Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_warning "Docker Compose no está instalado. Algunas funcionalidades no estarán disponibles."
    fi
    
    print_success "Prerrequisitos verificados"
}

# Instalar dependencias
install_dependencies() {
    print_status "Instalando dependencias..."
    
    # Root dependencies
    print_status "Instalando dependencias raíz..."
    npm install
    
    # Backend dependencies
    print_status "Instalando dependencias del backend..."
    cd backend
    npm install
    cd ..
    
    # Frontend dependencies
    print_status "Instalando dependencias del frontend..."
    cd frontend
    npm install
    cd ..
    
    print_success "Dependencias instaladas"
}

# Configurar variables de entorno
setup_environment() {
    print_status "Configurando variables de entorno..."
    
    # Backend .env
    if [ ! -f "backend/.env" ]; then
        print_status "Creando backend/.env desde template..."
        cp backend/.env.example backend/.env
        print_warning "Por favor editar backend/.env con tus configuraciones específicas"
    else
        print_status "backend/.env ya existe"
    fi
    
    print_success "Variables de entorno configuradas"
}

# Configurar base de datos
setup_database() {
    print_status "Configurando base de datos..."
    
    if command -v docker &> /dev/null && command -v docker-compose &> /dev/null; then
        print_status "Iniciando PostgreSQL con Docker..."
        docker-compose up -d database redis
        
        # Esperar a que la base de datos esté lista
        print_status "Esperando a que PostgreSQL esté listo..."
        sleep 10
        
        # Generar cliente Prisma
        print_status "Generando cliente Prisma..."
        cd backend
        npx prisma generate
        
        # Ejecutar migraciones
        print_status "Ejecutando migraciones de base de datos..."
        npx prisma migrate dev --name init
        
        # Seed inicial (opcional)
        print_status "Ejecutando seed inicial..."
        npm run db:seed || print_warning "Seed falló, continuando..."
        
        cd ..
        
        print_success "Base de datos configurada"
    else
        print_warning "Docker no disponible. Configurar PostgreSQL manualmente."
        print_warning "Conexión requerida: postgresql://pautas_user:pautas_password@localhost:5432/pautas_publicitarias"
    fi
}

# Crear directorios necesarios
create_directories() {
    print_status "Creando directorios necesarios..."
    
    mkdir -p backend/logs
    mkdir -p backend/media
    mkdir -p backend/dinesat-output
    mkdir -p backend/backups
    mkdir -p frontend/dist
    
    print_success "Directorios creados"
}

# Verificar configuración
verify_setup() {
    print_status "Verificando configuración..."
    
    # Verificar que los archivos necesarios existen
    if [ ! -f "backend/.env" ]; then
        print_error "backend/.env no encontrado"
        return 1
    fi
    
    if [ ! -f "backend/package.json" ]; then
        print_error "backend/package.json no encontrado"
        return 1
    fi
    
    if [ ! -f "frontend/package.json" ]; then
        print_error "frontend/package.json no encontrado"
        return 1
    fi
    
    print_success "Configuración verificada"
}

# Mostrar información de siguiente pasos
show_next_steps() {
    echo ""
    echo "🎉 ¡Configuración completada exitosamente!"
    echo ""
    echo "📋 Próximos pasos:"
    echo ""
    echo "1. Editar configuración:"
    echo "   nano backend/.env"
    echo ""
    echo "2. Ejecutar en desarrollo:"
    echo "   npm run dev"
    echo ""
    echo "3. O ejecutar con Docker:"
    echo "   npm run docker:up"
    echo ""
    echo "4. Acceder a la aplicación:"
    echo "   Frontend: http://localhost:3000"
    echo "   Backend:  http://localhost:3001"
    echo "   Grafana:  http://localhost:3002 (admin/admin)"
    echo ""
    echo "📚 Documentación disponible en:"
    echo "   - README_PROYECTO.md"
    echo "   - docs/"
    echo ""
    echo "🆘 Para soporte:"
    echo "   - Revisar logs en backend/logs/"
    echo "   - Consultar documentación técnica"
    echo ""
}

# Función principal
main() {
    echo "=================================================="
    echo "  Sistema de Gestión de Pautas Publicitarias"
    echo "  Setup Script v1.0"
    echo "=================================================="
    echo ""
    
    check_prerequisites
    install_dependencies
    setup_environment
    create_directories
    setup_database
    verify_setup
    show_next_steps
    
    print_success "¡Setup completado! 🚀"
}

# Ejecutar función principal
main "$@"
