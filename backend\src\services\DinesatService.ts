import fs from 'fs/promises';
import path from 'path';
import chokidar, { FSWatcher } from 'chokidar';
import { format } from 'date-fns';
import { logger } from '@/utils/logger';
import { EventEmitter } from 'events';

export interface DinesatConfig {
  outputPath: string;
  format: 'M3U' | 'TXT' | 'XML';
  fileEncoding?: 'utf8' | 'latin1';
  lineEnding?: '\n' | '\r\n';
  watchDirectory?: boolean;
  logParsingInterval?: number;
  enableBackup?: boolean;
  backupRetentionDays?: number;
}

export interface Schedule {
  id: string;
  scheduledTime: Date;
  duration: number;
  advertisement: {
    id: string;
    name: string;
    code: string;
    audioFilePath: string;
    client: {
      name: string;
    };
  };
}

export interface DinesatLogEntry {
  timestamp: Date;
  action: 'PLAYED' | 'SKIPPED' | 'ERROR';
  filePath: string;
  duration: number;
  rawLine: string;
}

export class DinesatError extends Error {
  constructor(message: string, public code: string = 'UNKNOWN') {
    super(message);
    this.name = 'DinesatError';
  }
}

export class DinesatService extends EventEmitter {
  private config: DinesatConfig;
  private watcher: FSWatcher | null = null;
  private logParsingInterval: NodeJS.Timeout | null = null;
  private lastLogPosition: number = 0;

  constructor(config: DinesatConfig) {
    super();
    this.config = {
      fileEncoding: 'utf8',
      lineEnding: '\r\n',
      watchDirectory: false,
      logParsingInterval: 5000,
      enableBackup: true,
      backupRetentionDays: 30,
      ...config
    };

    this.ensureDirectoryExists();
    
    if (this.config.watchDirectory) {
      this.startDirectoryWatching();
    }
  }

  /**
   * Generate playlist file for given schedules and date
   */
  async generatePlaylist(schedules: Schedule[], date: Date): Promise<string> {
    try {
      const fileName = `pauta_${format(date, 'yyyy-MM-dd')}.${this.config.format.toLowerCase()}`;
      const filePath = path.join(this.config.outputPath, fileName);
      
      let content: string;
      
      switch (this.config.format) {
        case 'M3U':
          content = this.generateM3U(schedules, date);
          break;
        case 'TXT':
          content = this.generateTXT(schedules);
          break;
        case 'XML':
          content = this.generateXML(schedules, date);
          break;
        default:
          throw new DinesatError(`Formato no soportado: ${this.config.format}`);
      }
      
      // Atomic write
      await this.writeFileAtomic(filePath, content);
      
      // Create backup if enabled
      if (this.config.enableBackup) {
        await this.createBackup(filePath);
      }
      
      logger.info(`Playlist generada: ${filePath}`);
      this.emit('playlist:generated', { filePath, schedules: schedules.length });
      
      return filePath;
    } catch (error) {
      logger.error('Error generando playlist:', error);
      throw new DinesatError(`Error generando playlist: ${error.message}`);
    }
  }

  /**
   * Generate M3U format
   */
  private generateM3U(schedules: Schedule[], date: Date): string {
    let content = '#EXTM3U' + this.config.lineEnding;
    content += `#PLAYLIST:Pauta Publicitaria - ${format(date, 'yyyy-MM-dd')}` + this.config.lineEnding;
    content += this.config.lineEnding;
    
    // Group by time blocks
    const blocks = this.groupByTimeBlocks(schedules);
    
    for (const [blockName, blockSchedules] of blocks) {
      content += `# ${blockName}` + this.config.lineEnding;
      
      for (const schedule of blockSchedules) {
        const ad = schedule.advertisement;
        content += `#EXTINF:${schedule.duration},${ad.name}` + this.config.lineEnding;
        content += `${ad.audioFilePath}` + this.config.lineEnding;
        content += this.config.lineEnding;
      }
    }
    
    return content;
  }

  /**
   * Generate TXT format
   */
  private generateTXT(schedules: Schedule[]): string {
    let content = '';
    
    // Sort by scheduled time
    const sortedSchedules = schedules.sort((a, b) => 
      a.scheduledTime.getTime() - b.scheduledTime.getTime()
    );
    
    for (const schedule of sortedSchedules) {
      const ad = schedule.advertisement;
      const timeStr = format(schedule.scheduledTime, 'HH:mm:ss');
      
      const line = [
        schedule.duration.toString(),
        ad.audioFilePath,
        ad.name,
        ad.client.name,
        ad.code,
        timeStr
      ].join('|');
      
      content += line + this.config.lineEnding;
    }
    
    return content;
  }

  /**
   * Generate XML format
   */
  private generateXML(schedules: Schedule[], date: Date): string {
    let xml = '<?xml version="1.0" encoding="UTF-8"?>' + this.config.lineEnding;
    xml += '<playlist>' + this.config.lineEnding;
    
    // Metadata
    xml += '  <metadata>' + this.config.lineEnding;
    xml += `    <date>${format(date, 'yyyy-MM-dd')}</date>` + this.config.lineEnding;
    xml += '    <station>Radio Éxito FM</station>' + this.config.lineEnding;
    xml += '    <generated_by>Sistema de Gestión de Pautas</generated_by>' + this.config.lineEnding;
    xml += `    <generated_at>${new Date().toISOString()}</generated_at>` + this.config.lineEnding;
    xml += '  </metadata>' + this.config.lineEnding;
    
    // Items
    xml += '  <items>' + this.config.lineEnding;
    
    schedules.forEach((schedule, index) => {
      const ad = schedule.advertisement;
      
      xml += '    <item>' + this.config.lineEnding;
      xml += `      <id>${index + 1}</id>` + this.config.lineEnding;
      xml += `      <scheduled_time>${format(schedule.scheduledTime, 'HH:mm:ss')}</scheduled_time>` + this.config.lineEnding;
      xml += `      <duration>${schedule.duration}</duration>` + this.config.lineEnding;
      xml += `      <file>${this.escapeXml(ad.audioFilePath)}</file>` + this.config.lineEnding;
      xml += `      <title>${this.escapeXml(ad.name)}</title>` + this.config.lineEnding;
      xml += '      <client>' + this.config.lineEnding;
      xml += `        <name>${this.escapeXml(ad.client.name)}</name>` + this.config.lineEnding;
      xml += `        <code>${this.escapeXml(ad.code)}</code>` + this.config.lineEnding;
      xml += '      </client>' + this.config.lineEnding;
      xml += '      <type>advertisement</type>' + this.config.lineEnding;
      xml += '      <priority>high</priority>' + this.config.lineEnding;
      xml += '    </item>' + this.config.lineEnding;
    });
    
    xml += '  </items>' + this.config.lineEnding;
    xml += '</playlist>' + this.config.lineEnding;
    
    return xml;
  }

  /**
   * Group schedules by time blocks
   */
  private groupByTimeBlocks(schedules: Schedule[]): Map<string, Schedule[]> {
    const blocks = new Map<string, Schedule[]>();
    
    for (const schedule of schedules) {
      const hour = schedule.scheduledTime.getHours();
      let blockName: string;
      
      if (hour >= 6 && hour < 12) {
        blockName = 'Bloque Mañana - 06:00-12:00';
      } else if (hour >= 12 && hour < 18) {
        blockName = 'Bloque Tarde - 12:00-18:00';
      } else {
        blockName = 'Bloque Noche - 18:00-24:00';
      }
      
      if (!blocks.has(blockName)) {
        blocks.set(blockName, []);
      }
      blocks.get(blockName)!.push(schedule);
    }
    
    return blocks;
  }

  /**
   * Start directory watching
   */
  private startDirectoryWatching(): void {
    this.watcher = chokidar.watch(this.config.outputPath, {
      ignored: /^\./, // Ignore hidden files
      persistent: true,
      ignoreInitial: true
    });
    
    this.watcher
      .on('add', (filePath) => this.handleFileAdded(filePath))
      .on('change', (filePath) => this.handleFileChanged(filePath))
      .on('unlink', (filePath) => this.handleFileDeleted(filePath))
      .on('error', (error) => this.handleWatcherError(error));
    
    logger.info(`Dinesat directory watcher started: ${this.config.outputPath}`);
  }

  /**
   * Stop directory watching
   */
  stopDirectoryWatching(): void {
    if (this.watcher) {
      this.watcher.close();
      this.watcher = null;
      logger.info('Dinesat directory watcher stopped');
    }
  }

  /**
   * Parse log entries from Dinesat log file
   */
  async parseLogEntries(logFilePath: string): Promise<DinesatLogEntry[]> {
    try {
      const stats = await fs.stat(logFilePath);
      
      if (stats.size <= this.lastLogPosition) {
        return []; // No new entries
      }
      
      const buffer = Buffer.alloc(stats.size - this.lastLogPosition);
      const fileHandle = await fs.open(logFilePath, 'r');
      
      await fileHandle.read(buffer, 0, buffer.length, this.lastLogPosition);
      await fileHandle.close();
      
      const content = buffer.toString('utf8');
      const lines = content.split('\n').filter(line => line.trim());
      
      const entries: DinesatLogEntry[] = [];
      
      for (const line of lines) {
        const entry = this.parseLogLine(line);
        if (entry) {
          entries.push(entry);
        }
      }
      
      this.lastLogPosition = stats.size;
      return entries;
      
    } catch (error) {
      throw new DinesatError(`Error parsing log file: ${error.message}`);
    }
  }

  /**
   * Parse single log line
   */
  private parseLogLine(line: string): DinesatLogEntry | null {
    // Format: "2024-06-15 14:30:25 [PLAYED] C:\Audio\Anuncios\cliente_abc.mp3 (30s)"
    const regex = /(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) \[(\w+)\] (.+) \((\d+)s\)/;
    const match = line.match(regex);
    
    if (!match) return null;
    
    return {
      timestamp: new Date(match[1]),
      action: match[2] as 'PLAYED' | 'SKIPPED' | 'ERROR',
      filePath: match[3],
      duration: parseInt(match[4]),
      rawLine: line
    };
  }

  /**
   * Ensure output directory exists
   */
  private async ensureDirectoryExists(): Promise<void> {
    try {
      await fs.access(this.config.outputPath);
    } catch {
      await fs.mkdir(this.config.outputPath, { recursive: true });
      logger.info(`Created Dinesat output directory: ${this.config.outputPath}`);
    }
  }

  /**
   * Atomic file write
   */
  private async writeFileAtomic(filePath: string, content: string): Promise<void> {
    const tempPath = `${filePath}.tmp`;
    
    await fs.writeFile(tempPath, content, { encoding: this.config.fileEncoding });
    await fs.rename(tempPath, filePath);
  }

  /**
   * Create backup of file
   */
  private async createBackup(filePath: string): Promise<void> {
    if (!this.config.enableBackup) return;
    
    const backupDir = path.join(this.config.outputPath, 'backup');
    await fs.mkdir(backupDir, { recursive: true });
    
    const fileName = path.basename(filePath);
    const timestamp = format(new Date(), 'yyyy-MM-dd_HH-mm-ss');
    const backupPath = path.join(backupDir, `${timestamp}_${fileName}`);
    
    await fs.copyFile(filePath, backupPath);
  }

  /**
   * Escape XML special characters
   */
  private escapeXml(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  }

  /**
   * File event handlers
   */
  private handleFileAdded(filePath: string): void {
    logger.info(`Dinesat file added: ${filePath}`);
    this.emit('file:added', { filePath });
  }

  private handleFileChanged(filePath: string): void {
    logger.info(`Dinesat file changed: ${filePath}`);
    this.emit('file:changed', { filePath });
  }

  private handleFileDeleted(filePath: string): void {
    logger.info(`Dinesat file deleted: ${filePath}`);
    this.emit('file:deleted', { filePath });
  }

  private handleWatcherError(error: Error): void {
    logger.error('Dinesat directory watcher error:', error);
    this.emit('watcher:error', { error });
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    this.stopDirectoryWatching();
    
    if (this.logParsingInterval) {
      clearInterval(this.logParsingInterval);
      this.logParsingInterval = null;
    }
    
    this.removeAllListeners();
  }
}
