<!doctype html>
<html lang="es">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>🎯 Sistema de Pautas Publicitarias</title>
    <meta name="description" content="Sistema unificado de gestión de pautas publicitarias para TV y Radio" />
    
    <!-- Preload fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
      /* Loading screen */
      #loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: #1A202C;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        color: #E2E8F0;
        font-family: 'Inter', sans-serif;
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid #4A5568;
        border-top: 4px solid #3182CE;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 1rem;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .loading-text {
        font-size: 1.2rem;
        font-weight: 500;
        margin-bottom: 0.5rem;
      }
      
      .loading-subtitle {
        font-size: 0.9rem;
        opacity: 0.7;
      }
    </style>
  </head>
  <body>
    <!-- Loading Screen -->
    <div id="loading-screen">
      <div class="loading-spinner"></div>
      <div class="loading-text">🎯 Sistema de Pautas Publicitarias</div>
      <div class="loading-subtitle">Cargando aplicación...</div>
    </div>
    
    <!-- React App Root -->
    <div id="root"></div>
    
    <script type="module" src="/src/main.tsx"></script>
    
    <script>
      // Hide loading screen when React app loads
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loadingScreen = document.getElementById('loading-screen');
          if (loadingScreen) {
            loadingScreen.style.opacity = '0';
            loadingScreen.style.transition = 'opacity 0.5s ease';
            setTimeout(function() {
              loadingScreen.style.display = 'none';
            }, 500);
          }
        }, 1000);
      });
    </script>
  </body>
</html>
