# Esquema de Base de Datos - Sistema de Gestión de Pautas Publicitarias

## Visión General

El esquema de base de datos está diseñado para soportar la gestión completa de pautas publicitarias, desde la creación de anuncios hasta el registro de emisiones y generación de reportes.

## Tablas Principales

### 1. <PERSON><PERSON><PERSON> (clients)
```sql
CREATE TABLE clients (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    contact_name VARCHAR(255),
    email VARCHAR(255),
    phone VARCHAR(50),
    address TEXT,
    tax_id VARCHAR(50),
    status client_status DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TYPE client_status AS ENUM ('active', 'inactive', 'suspended');
```

### 2. <PERSON><PERSON><PERSON><PERSON> (advertisements)
```sql
CREATE TABLE advertisements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(100) UNIQUE NOT NULL,
    duration_seconds INTEGER NOT NULL,
    video_file_path VARCHAR(500), -- Para vMix
    audio_file_path VARCHAR(500), -- Para Dinesat
    campaign_start_date DATE NOT NULL,
    campaign_end_date DATE NOT NULL,
    status ad_status DEFAULT 'active',
    metadata JSONB, -- Información adicional flexible
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TYPE ad_status AS ENUM ('active', 'inactive', 'expired', 'pending');
```

### 3. Programación (schedules)
```sql
CREATE TABLE schedules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    advertisement_id UUID NOT NULL REFERENCES advertisements(id) ON DELETE CASCADE,
    medium_type medium_type NOT NULL,
    scheduled_date DATE NOT NULL,
    scheduled_time TIME NOT NULL,
    duration_seconds INTEGER NOT NULL,
    status schedule_status DEFAULT 'scheduled',
    recurrence_pattern JSONB, -- Para programaciones recurrentes
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TYPE medium_type AS ENUM ('tv', 'radio');
CREATE TYPE schedule_status AS ENUM ('scheduled', 'executed', 'failed', 'cancelled');
```

### 4. Logs de Emisión (emission_logs)
```sql
CREATE TABLE emission_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    schedule_id UUID NOT NULL REFERENCES schedules(id),
    advertisement_id UUID NOT NULL REFERENCES advertisements(id),
    client_id UUID NOT NULL REFERENCES clients(id),
    medium_type medium_type NOT NULL,
    scheduled_datetime TIMESTAMP NOT NULL,
    executed_datetime TIMESTAMP,
    status emission_status NOT NULL,
    error_message TEXT,
    vmix_response JSONB, -- Respuesta de vMix API
    dinesat_file_path VARCHAR(500), -- Ruta del archivo generado para Dinesat
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TYPE emission_status AS ENUM ('pending', 'sent', 'confirmed', 'failed');
```

### 5. Usuarios (users)
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    role user_role DEFAULT 'operator',
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TYPE user_role AS ENUM ('admin', 'manager', 'operator', 'viewer');
```

### 6. Configuración del Sistema (system_config)
```sql
CREATE TABLE system_config (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    key VARCHAR(100) UNIQUE NOT NULL,
    value JSONB NOT NULL,
    description TEXT,
    updated_by UUID REFERENCES users(id),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 7. Reportes Generados (generated_reports)
```sql
CREATE TABLE generated_reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_id UUID REFERENCES clients(id),
    report_type report_type NOT NULL,
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    file_path VARCHAR(500),
    status report_status DEFAULT 'generating',
    generated_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TYPE report_type AS ENUM ('proof_of_play', 'campaign_summary', 'billing');
CREATE TYPE report_status AS ENUM ('generating', 'completed', 'failed', 'sent');
```

## Índices para Optimización

```sql
-- Índices para consultas frecuentes
CREATE INDEX idx_schedules_date_time ON schedules(scheduled_date, scheduled_time);
CREATE INDEX idx_schedules_medium ON schedules(medium_type);
CREATE INDEX idx_emission_logs_datetime ON emission_logs(scheduled_datetime);
CREATE INDEX idx_emission_logs_client ON emission_logs(client_id);
CREATE INDEX idx_advertisements_campaign ON advertisements(campaign_start_date, campaign_end_date);
CREATE INDEX idx_advertisements_client ON advertisements(client_id);
CREATE INDEX idx_advertisements_status ON advertisements(status);

-- Índice compuesto para búsquedas de programación
CREATE INDEX idx_schedules_composite ON schedules(scheduled_date, medium_type, status);

-- Índice para reportes por período
CREATE INDEX idx_emission_logs_period ON emission_logs(scheduled_datetime, client_id, medium_type);
```

## Triggers para Auditoría

```sql
-- Función para actualizar timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers para actualización automática
CREATE TRIGGER update_clients_updated_at BEFORE UPDATE ON clients
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_advertisements_updated_at BEFORE UPDATE ON advertisements
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_schedules_updated_at BEFORE UPDATE ON schedules
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

## Vistas para Consultas Comunes

```sql
-- Vista para programación con información completa
CREATE VIEW schedule_details AS
SELECT 
    s.id,
    s.scheduled_date,
    s.scheduled_time,
    s.duration_seconds,
    s.medium_type,
    s.status,
    a.name as ad_name,
    a.code as ad_code,
    c.name as client_name,
    u.full_name as created_by_name
FROM schedules s
JOIN advertisements a ON s.advertisement_id = a.id
JOIN clients c ON a.client_id = c.id
JOIN users u ON s.created_by = u.id;

-- Vista para reportes de emisión
CREATE VIEW emission_report AS
SELECT 
    el.id,
    el.scheduled_datetime,
    el.executed_datetime,
    el.status,
    el.medium_type,
    a.name as ad_name,
    a.code as ad_code,
    a.duration_seconds,
    c.name as client_name,
    c.id as client_id
FROM emission_logs el
JOIN advertisements a ON el.advertisement_id = a.id
JOIN clients c ON el.client_id = c.id;
```

## Datos de Configuración Inicial

```sql
-- Configuraciones del sistema
INSERT INTO system_config (key, value, description) VALUES
('vmix_api_url', '"http://localhost:8088"', 'URL de la API de vMix'),
('vmix_api_timeout', '5000', 'Timeout en ms para llamadas a vMix'),
('dinesat_output_path', '"/path/to/dinesat/playlists"', 'Directorio de salida para archivos Dinesat'),
('report_email_from', '"<EMAIL>"', 'Email remitente para reportes'),
('max_schedule_days_ahead', '90', 'Máximo días adelante para programar');

-- Usuario administrador inicial
INSERT INTO users (username, email, password_hash, full_name, role) VALUES
('admin', '<EMAIL>', '$2b$10$...', 'Administrador del Sistema', 'admin');
```

## Consideraciones de Rendimiento

### Particionamiento
Para sistemas con alto volumen, considerar particionamiento por fecha en `emission_logs`:

```sql
-- Particionamiento mensual para logs de emisión
CREATE TABLE emission_logs_y2024m01 PARTITION OF emission_logs
    FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');
```

### Archivado de Datos
Implementar estrategia de archivado para datos antiguos:
- Logs de emisión > 2 años → tabla de archivo
- Reportes generados > 1 año → almacenamiento externo
- Programaciones ejecutadas > 6 meses → compresión

### Backup y Recuperación
- Backup completo diario
- Backup incremental cada 4 horas
- Punto de recuperación objetivo (RPO): 4 horas
- Tiempo de recuperación objetivo (RTO): 1 hora
