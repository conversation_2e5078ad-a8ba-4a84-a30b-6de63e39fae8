# Sistema de Gestión de Pautas Publicitarias

## 🚀 Proyecto Creado

¡El proyecto completo ha sido creado exitosamente! Este es un sistema moderno y unificado para la gestión de pautas publicitarias que integra televisión (vMix) y radio (Dinesat).

## 📁 Estructura del Proyecto

```
sistema-pautas-publicitarias/
├── backend/                 # API Node.js + TypeScript
│   ├── src/
│   │   ├── services/       # VMixService, DinesatService, SchedulerService
│   │   ├── controllers/    # Controladores de API
│   │   ├── middleware/     # Autenticación, validación, errores
│   │   ├── routes/         # Rutas de la API
│   │   └── utils/          # Utilidades y helpers
│   ├── prisma/             # Esquema de base de datos
│   ├── Dockerfile          # Contenedor backend
│   └── package.json
├── frontend/               # React + TypeScript + Tailwind
│   ├── src/
│   │   ├── components/     # Componentes reutilizables
│   │   ├── pages/          # Páginas principales
│   │   ├── services/       # Servicios de API
│   │   ├── hooks/          # Hooks personalizados
│   │   └── store/          # Estado global (Zustand)
│   ├── Dockerfile          # Contenedor frontend
│   └── package.json
├── docs/                   # Documentación técnica completa
├── docker-compose.yml      # Orquestación de contenedores
└── package.json           # Scripts del proyecto
```

## 🛠️ Tecnologías Implementadas

### Backend
- **Node.js 20** + **Express** + **TypeScript**
- **PostgreSQL 15** con **Prisma ORM**
- **Redis** para cache y sesiones
- **Socket.io** para WebSocket en tiempo real
- **JWT** para autenticación
- **Winston** para logging
- **Jest** para testing

### Frontend
- **React 18** + **TypeScript** + **Vite**
- **Tailwind CSS** con diseño anti-genérico
- **Radix UI** para componentes base
- **Zustand** para estado global
- **React Query** para manejo de datos
- **React Big Calendar** para programación
- **Socket.io Client** para tiempo real

### Infraestructura
- **Docker** + **Docker Compose**
- **Nginx** como reverse proxy
- **Prometheus** + **Grafana** para monitoreo
- **ELK Stack** para logs centralizados

## 🚀 Instalación y Ejecución

### Prerrequisitos
- Node.js 18+
- Docker y Docker Compose
- Git

### 1. Clonar y Configurar
```bash
# Instalar dependencias
npm run setup

# Configurar variables de entorno
cp backend/.env.example backend/.env
# Editar backend/.env con tus configuraciones
```

### 2. Ejecutar con Docker (Recomendado)
```bash
# Construir y ejecutar todos los servicios
npm run docker:up

# Ver logs
docker-compose logs -f

# Detener servicios
npm run docker:down
```

### 3. Ejecutar en Desarrollo
```bash
# Terminal 1: Base de datos
docker-compose up database redis

# Terminal 2: Backend
npm run dev:backend

# Terminal 3: Frontend
npm run dev:frontend
```

## 🌐 Acceso a la Aplicación

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:3001
- **Grafana**: http://localhost:3002 (admin/admin)
- **Prometheus**: http://localhost:9090

## 📊 Características Principales

### ✅ Dashboard de Control
- Vista unificada de TV y Radio
- Métricas en tiempo real
- Timeline visual del día
- Alertas y notificaciones

### ✅ Gestión de Anuncios
- Biblioteca de medios centralizada
- Preview integrado de videos/audios
- Gestión de campañas con fechas
- Estados visuales claros

### ✅ Programación Visual
- Calendario drag & drop
- Detección automática de conflictos
- Programación recurrente
- Separación clara TV/Radio

### ✅ Integraciones
- **vMix**: Control HTTP API completo
- **Dinesat**: Generación automática de playlists
- **Proof-of-Play**: Logging automático
- **Reportes**: Generación PDF/Excel

### ✅ Características Anti-Genéricas
- Paleta de colores broadcast profesional
- Iconografía personalizada
- Animaciones funcionales
- Diseño responsive

## 🔧 Configuración de Integraciones

### vMix (Televisión)
1. Abrir vMix
2. Habilitar Web Controller (Settings → Web Controller)
3. Puerto: 8088 (default)
4. Configurar `VMIX_API_URL` en `.env`

### Dinesat (Radio)
1. Configurar directorio de salida en `DINESAT_OUTPUT_PATH`
2. Elegir formato: M3U, TXT o XML
3. Configurar monitoreo de logs si está disponible

## 📈 Monitoreo y Logs

### Prometheus Métricas
- Requests por segundo
- Tiempo de respuesta
- Errores de integración
- Estado de servicios

### Grafana Dashboards
- Dashboard principal del sistema
- Métricas de vMix/Dinesat
- Performance de la aplicación
- Alertas configurables

### Logging Centralizado
- Logs estructurados con Winston
- Niveles: error, warn, info, debug
- Rotación automática de archivos
- Integración con ELK Stack

## 🧪 Testing

```bash
# Backend tests
npm run test:backend

# Frontend tests
npm run test:frontend

# Coverage reports
npm run test:coverage
```

## 📚 Documentación Adicional

- [Arquitectura del Sistema](docs/system_architecture.md)
- [Esquema de Base de Datos](docs/database_schema.md)
- [Integración vMix](docs/vmix_integration_examples.md)
- [Integración Dinesat](docs/dinesat_integration_examples.md)
- [Especificaciones UI](docs/ui_design_specifications.md)
- [Plan de Desarrollo](docs/development_plan.md)

## 🔒 Seguridad

- Autenticación JWT con refresh tokens
- Rate limiting por IP
- Validación de entrada con Zod
- Headers de seguridad con Helmet
- CORS configurado
- Sanitización de archivos subidos

## 🚀 Deployment en Producción

### Variables de Entorno Críticas
```bash
NODE_ENV=production
JWT_SECRET=your-super-secure-secret
DATABASE_URL=********************************/db
VMIX_API_URL=http://vmix-server:8088
```

### SSL/HTTPS
- Configurar certificados en `nginx/ssl/`
- Actualizar `docker-compose.yml` para HTTPS
- Configurar redirects HTTP → HTTPS

### Backup y Recuperación
- Backup automático de PostgreSQL
- Backup de archivos de media
- Retención configurable
- Scripts de restauración

## 🤝 Contribución

1. Fork del proyecto
2. Crear rama feature (`git checkout -b feature/nueva-funcionalidad`)
3. Commit cambios (`git commit -am 'Agregar nueva funcionalidad'`)
4. Push a la rama (`git push origin feature/nueva-funcionalidad`)
5. Crear Pull Request

## 📄 Licencia

MIT License - ver archivo LICENSE para detalles.

## 🆘 Soporte

Para soporte técnico o preguntas:
- Revisar documentación en `/docs`
- Crear issue en GitHub
- Consultar logs en `backend/logs/`

---

**¡El sistema está listo para revolucionar la gestión de pautas publicitarias!** 🎉
