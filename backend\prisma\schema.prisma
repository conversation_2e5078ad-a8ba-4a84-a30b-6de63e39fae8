// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum ClientStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
}

enum AdStatus {
  ACTIVE
  INACTIVE
  EXPIRED
  PENDING
}

enum MediumType {
  TV
  RADIO
}

enum ScheduleStatus {
  SCHEDULED
  EXECUTED
  FAILED
  CANCELLED
}

enum EmissionStatus {
  PENDING
  SENT
  CONFIRMED
  FAILED
}

enum UserRole {
  ADMIN
  MANAGER
  OPERATOR
  VIEWER
}

enum ReportType {
  PROOF_OF_PLAY
  CAMPAIGN_SUMMARY
  BILLING
}

enum ReportStatus {
  GENERATING
  COMPLETED
  FAILED
  SENT
}

model User {
  id           String    @id @default(uuid())
  username     String    @unique
  email        String    @unique
  passwordHash String    @map("password_hash")
  fullName     String    @map("full_name")
  role         User<PERSON><PERSON>  @default(OPERATOR)
  isActive     Boolean   @default(true) @map("is_active")
  lastLogin    DateTime? @map("last_login")
  createdAt    DateTime  @default(now()) @map("created_at")
  updatedAt    DateTime  @updatedAt @map("updated_at")

  // Relations
  schedules        Schedule[]
  generatedReports GeneratedReport[]
  systemConfigs    SystemConfig[]

  @@map("users")
}

model Client {
  id          String       @id @default(uuid())
  name        String
  contactName String?      @map("contact_name")
  email       String?
  phone       String?
  address     String?
  taxId       String?      @map("tax_id")
  status      ClientStatus @default(ACTIVE)
  createdAt   DateTime     @default(now()) @map("created_at")
  updatedAt   DateTime     @updatedAt @map("updated_at")

  // Relations
  advertisements   Advertisement[]
  emissionLogs     EmissionLog[]
  generatedReports GeneratedReport[]

  @@map("clients")
}

model Advertisement {
  id                String    @id @default(uuid())
  clientId          String    @map("client_id")
  name              String
  code              String    @unique
  durationSeconds   Int       @map("duration_seconds")
  videoFilePath     String?   @map("video_file_path")
  audioFilePath     String?   @map("audio_file_path")
  campaignStartDate DateTime  @map("campaign_start_date") @db.Date
  campaignEndDate   DateTime  @map("campaign_end_date") @db.Date
  status            AdStatus  @default(ACTIVE)
  metadata          Json?
  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @updatedAt @map("updated_at")

  // Relations
  client       Client        @relation(fields: [clientId], references: [id], onDelete: Cascade)
  schedules    Schedule[]
  emissionLogs EmissionLog[]

  @@map("advertisements")
}

model Schedule {
  id                String         @id @default(uuid())
  advertisementId   String         @map("advertisement_id")
  mediumType        MediumType     @map("medium_type")
  scheduledDate     DateTime       @map("scheduled_date") @db.Date
  scheduledTime     DateTime       @map("scheduled_time") @db.Time
  durationSeconds   Int            @map("duration_seconds")
  status            ScheduleStatus @default(SCHEDULED)
  recurrencePattern Json?          @map("recurrence_pattern")
  createdBy         String         @map("created_by")
  createdAt         DateTime       @default(now()) @map("created_at")
  updatedAt         DateTime       @updatedAt @map("updated_at")

  // Relations
  advertisement Advertisement @relation(fields: [advertisementId], references: [id], onDelete: Cascade)
  createdByUser User          @relation(fields: [createdBy], references: [id])
  emissionLogs  EmissionLog[]

  @@index([scheduledDate, scheduledTime])
  @@index([mediumType])
  @@index([status])
  @@map("schedules")
}

model EmissionLog {
  id                  String          @id @default(uuid())
  scheduleId          String          @map("schedule_id")
  advertisementId     String          @map("advertisement_id")
  clientId            String          @map("client_id")
  mediumType          MediumType      @map("medium_type")
  scheduledDatetime   DateTime        @map("scheduled_datetime")
  executedDatetime    DateTime?       @map("executed_datetime")
  status              EmissionStatus
  errorMessage        String?         @map("error_message")
  vmixResponse        Json?           @map("vmix_response")
  dinesatFilePath     String?         @map("dinesat_file_path")
  createdAt           DateTime        @default(now()) @map("created_at")

  // Relations
  schedule      Schedule      @relation(fields: [scheduleId], references: [id])
  advertisement Advertisement @relation(fields: [advertisementId], references: [id])
  client        Client        @relation(fields: [clientId], references: [id])

  @@index([scheduledDatetime])
  @@index([clientId])
  @@index([mediumType])
  @@map("emission_logs")
}

model SystemConfig {
  id          String   @id @default(uuid())
  key         String   @unique
  value       Json
  description String?
  updatedBy   String?  @map("updated_by")
  updatedAt   DateTime @default(now()) @map("updated_at")

  // Relations
  updatedByUser User? @relation(fields: [updatedBy], references: [id])

  @@map("system_config")
}

model GeneratedReport {
  id          String       @id @default(uuid())
  clientId    String?      @map("client_id")
  reportType  ReportType   @map("report_type")
  periodStart DateTime     @map("period_start") @db.Date
  periodEnd   DateTime     @map("period_end") @db.Date
  filePath    String?      @map("file_path")
  status      ReportStatus @default(GENERATING)
  generatedBy String       @map("generated_by")
  createdAt   DateTime     @default(now()) @map("created_at")

  // Relations
  client        Client? @relation(fields: [clientId], references: [id])
  generatedByUser User  @relation(fields: [generatedBy], references: [id])

  @@map("generated_reports")
}
