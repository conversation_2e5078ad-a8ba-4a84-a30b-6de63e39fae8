@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

@layer base {
  :root {
    --background: 210 40% 98%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
  }
}

/* Broadcast-specific styles */
@layer components {
  .broadcast-card {
    @apply bg-studio-gray border border-studio-panel rounded-lg p-4 shadow-broadcast;
  }

  .broadcast-button {
    @apply bg-broadcast-blue hover:bg-broadcast-blue/90 text-white font-medium px-4 py-2 rounded-md transition-colors;
  }

  .broadcast-button-danger {
    @apply bg-broadcast-red hover:bg-broadcast-red/90 text-white font-medium px-4 py-2 rounded-md transition-colors;
  }

  .broadcast-button-success {
    @apply bg-broadcast-green hover:bg-broadcast-green/90 text-white font-medium px-4 py-2 rounded-md transition-colors;
  }

  .broadcast-input {
    @apply bg-studio-black border border-studio-panel rounded-md px-3 py-2 text-studio-light placeholder:text-studio-light/60 focus:outline-none focus:ring-2 focus:ring-broadcast-blue focus:border-transparent;
  }

  .status-indicator {
    @apply inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium;
  }

  .status-online {
    @apply bg-broadcast-green/20 text-broadcast-green border border-broadcast-green/30;
  }

  .status-offline {
    @apply bg-studio-panel/20 text-studio-light/60 border border-studio-panel/30;
  }

  .status-warning {
    @apply bg-broadcast-amber/20 text-broadcast-amber border border-broadcast-amber/30;
  }

  .status-error {
    @apply bg-broadcast-red/20 text-broadcast-red border border-broadcast-red/30;
  }

  .timeline-container {
    @apply bg-studio-gray rounded-lg border border-studio-panel overflow-hidden;
  }

  .timeline-header {
    @apply bg-studio-black border-b border-studio-panel p-4;
  }

  .timeline-body {
    @apply p-4;
  }

  .timeline-slot {
    @apply border border-studio-panel/50 rounded-md p-2 min-h-[60px] hover:border-broadcast-blue/50 transition-colors cursor-pointer;
  }

  .timeline-slot.occupied {
    @apply bg-broadcast-blue/20 border-broadcast-blue;
  }

  .timeline-slot.tv {
    @apply bg-broadcast-blue/10 border-broadcast-blue/50;
  }

  .timeline-slot.radio {
    @apply bg-frequency-purple/10 border-frequency-purple/50;
  }

  .ad-card {
    @apply broadcast-card hover:shadow-studio transition-shadow cursor-pointer;
  }

  .ad-card.dragging {
    @apply opacity-50 transform rotate-2;
  }

  .metric-card {
    @apply broadcast-card text-center;
  }

  .metric-value {
    @apply text-2xl font-bold text-broadcast-green;
  }

  .metric-label {
    @apply text-sm text-studio-light/70 mt-1;
  }

  .nav-link {
    @apply flex items-center gap-3 px-4 py-3 text-studio-light/70 hover:text-studio-light hover:bg-studio-panel/50 rounded-md transition-colors;
  }

  .nav-link.active {
    @apply text-studio-light bg-broadcast-blue/20 border-r-2 border-broadcast-blue;
  }

  .page-header {
    @apply flex items-center justify-between mb-6;
  }

  .page-title {
    @apply text-2xl font-bold text-studio-light;
  }

  .loading-spinner {
    @apply animate-spin rounded-full h-6 w-6 border-2 border-studio-light/20 border-t-broadcast-blue;
  }
}

/* Custom scrollbar */
@layer utilities {
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: theme('colors.studio.panel') theme('colors.studio.gray');
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: theme('colors.studio.gray');
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: theme('colors.studio.panel');
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: theme('colors.broadcast.blue');
  }
}

/* Animation utilities */
@layer utilities {
  .animate-pulse-slow {
    animation: pulse 3s infinite;
  }

  .animate-fade-in {
    animation: fade-in 0.3s ease-out;
  }

  .animate-slide-in {
    animation: slide-in 0.3s ease-out;
  }
}

/* Focus styles for accessibility */
@layer utilities {
  .focus-broadcast {
    @apply focus:outline-none focus:ring-2 focus:ring-broadcast-blue focus:ring-offset-2 focus:ring-offset-studio-black;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .broadcast-card {
    @apply border-2;
  }
  
  .status-indicator {
    @apply border-2;
  }
}
