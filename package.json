{"name": "sistema-pautas-publicitarias", "version": "1.0.0", "description": "Sistema unificado de gestión de pautas publicitarias para TV (vMix) y Radio (Dinesat)", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "start": "cd backend && npm start", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "setup": "npm run setup:backend && npm run setup:frontend", "setup:backend": "cd backend && npm install", "setup:frontend": "cd frontend && npm install", "db:migrate": "cd backend && npm run db:migrate", "db:seed": "cd backend && npm run db:seed"}, "keywords": ["broadcast", "advertising", "vmix", "dinesat", "television", "radio", "automation"], "author": "Sistema de Pautas Publicitarias", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "workspaces": ["backend", "frontend"]}