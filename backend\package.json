{"name": "pautas-backend", "version": "1.0.0", "description": "Backend API para Sistema de Gestión de Pautas Publicitarias", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:seed": "tsx src/database/seed.ts", "db:studio": "prisma studio", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "dependencies": {"@prisma/client": "^5.7.1", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "zod": "^3.22.4", "winston": "^3.11.0", "socket.io": "^4.7.4", "node-cron": "^3.0.3", "axios": "^1.6.2", "chokidar": "^3.5.3", "multer": "^1.4.5-lts.1", "sharp": "^0.33.1", "puppeteer": "^21.6.1", "nodemailer": "^6.9.7", "redis": "^4.6.11", "dotenv": "^16.3.1"}, "devDependencies": {"@types/node": "^20.10.4", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/compression": "^1.7.5", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/multer": "^1.4.11", "@types/nodemailer": "^6.4.14", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "typescript": "^5.3.3", "nodemon": "^3.0.2", "tsx": "^4.6.2", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "prisma": "^5.7.1", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0"}, "engines": {"node": ">=18.0.0"}}