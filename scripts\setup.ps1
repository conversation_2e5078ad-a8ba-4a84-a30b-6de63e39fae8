# Setup script para Sistema de Gestión de Pautas Publicitarias (Windows)
# Este script configura el entorno de desarrollo completo

param(
    [switch]$SkipDocker,
    [switch]$Help
)

if ($Help) {
    Write-Host "Setup Script para Sistema de Gestión de Pautas Publicitarias"
    Write-Host ""
    Write-Host "Uso: .\scripts\setup.ps1 [opciones]"
    Write-Host ""
    Write-Host "Opciones:"
    Write-Host "  -SkipDocker    Omitir configuración de Docker"
    Write-Host "  -Help          Mostrar esta ayuda"
    exit 0
}

# Función para imprimir mensajes coloreados
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Verificar prerrequisitos
function Test-Prerequisites {
    Write-Status "Verificando prerrequisitos..."
    
    # Node.js
    try {
        $nodeVersion = node --version
        $versionNumber = [int]($nodeVersion -replace 'v(\d+)\..*', '$1')
        if ($versionNumber -lt 18) {
            Write-Error "Node.js versión 18+ requerida. Versión actual: $nodeVersion"
            exit 1
        }
        Write-Status "Node.js $nodeVersion ✓"
    }
    catch {
        Write-Error "Node.js no está instalado. Por favor instalar Node.js 18+"
        exit 1
    }
    
    # npm
    try {
        $npmVersion = npm --version
        Write-Status "npm $npmVersion ✓"
    }
    catch {
        Write-Error "npm no está instalado"
        exit 1
    }
    
    # Docker (opcional)
    if (-not $SkipDocker) {
        try {
            $dockerVersion = docker --version
            Write-Status "Docker disponible ✓"
        }
        catch {
            Write-Warning "Docker no está instalado. Algunas funcionalidades no estarán disponibles."
        }
        
        try {
            $composeVersion = docker-compose --version
            Write-Status "Docker Compose disponible ✓"
        }
        catch {
            Write-Warning "Docker Compose no está instalado. Algunas funcionalidades no estarán disponibles."
        }
    }
    
    Write-Success "Prerrequisitos verificados"
}

# Instalar dependencias
function Install-Dependencies {
    Write-Status "Instalando dependencias..."
    
    # Root dependencies
    Write-Status "Instalando dependencias raíz..."
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Error instalando dependencias raíz"
        exit 1
    }
    
    # Backend dependencies
    Write-Status "Instalando dependencias del backend..."
    Set-Location backend
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Error instalando dependencias del backend"
        exit 1
    }
    Set-Location ..
    
    # Frontend dependencies
    Write-Status "Instalando dependencias del frontend..."
    Set-Location frontend
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Error instalando dependencias del frontend"
        exit 1
    }
    Set-Location ..
    
    Write-Success "Dependencias instaladas"
}

# Configurar variables de entorno
function Set-Environment {
    Write-Status "Configurando variables de entorno..."
    
    # Backend .env
    if (-not (Test-Path "backend\.env")) {
        Write-Status "Creando backend\.env desde template..."
        Copy-Item "backend\.env.example" "backend\.env"
        Write-Warning "Por favor editar backend\.env con tus configuraciones específicas"
    }
    else {
        Write-Status "backend\.env ya existe"
    }
    
    Write-Success "Variables de entorno configuradas"
}

# Configurar base de datos
function Set-Database {
    Write-Status "Configurando base de datos..."
    
    if (-not $SkipDocker -and (Get-Command docker -ErrorAction SilentlyContinue) -and (Get-Command docker-compose -ErrorAction SilentlyContinue)) {
        Write-Status "Iniciando PostgreSQL con Docker..."
        docker-compose up -d database redis
        
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Error iniciando servicios Docker"
            return
        }
        
        # Esperar a que la base de datos esté lista
        Write-Status "Esperando a que PostgreSQL esté listo..."
        Start-Sleep -Seconds 15
        
        # Generar cliente Prisma
        Write-Status "Generando cliente Prisma..."
        Set-Location backend
        npx prisma generate
        
        # Ejecutar migraciones
        Write-Status "Ejecutando migraciones de base de datos..."
        npx prisma migrate dev --name init
        
        # Seed inicial (opcional)
        Write-Status "Ejecutando seed inicial..."
        try {
            npm run db:seed
        }
        catch {
            Write-Warning "Seed falló, continuando..."
        }
        
        Set-Location ..
        
        Write-Success "Base de datos configurada"
    }
    else {
        Write-Warning "Docker no disponible o omitido. Configurar PostgreSQL manualmente."
        Write-Warning "Conexión requerida: postgresql://pautas_user:pautas_password@localhost:5432/pautas_publicitarias"
    }
}

# Crear directorios necesarios
function New-Directories {
    Write-Status "Creando directorios necesarios..."
    
    $directories = @(
        "backend\logs",
        "backend\media", 
        "backend\dinesat-output",
        "backend\backups",
        "frontend\dist"
    )
    
    foreach ($dir in $directories) {
        if (-not (Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
        }
    }
    
    Write-Success "Directorios creados"
}

# Verificar configuración
function Test-Setup {
    Write-Status "Verificando configuración..."
    
    $requiredFiles = @(
        "backend\.env",
        "backend\package.json",
        "frontend\package.json"
    )
    
    foreach ($file in $requiredFiles) {
        if (-not (Test-Path $file)) {
            Write-Error "$file no encontrado"
            return $false
        }
    }
    
    Write-Success "Configuración verificada"
    return $true
}

# Mostrar información de siguiente pasos
function Show-NextSteps {
    Write-Host ""
    Write-Host "🎉 ¡Configuración completada exitosamente!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 Próximos pasos:" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "1. Editar configuración:"
    Write-Host "   notepad backend\.env"
    Write-Host ""
    Write-Host "2. Ejecutar en desarrollo:"
    Write-Host "   npm run dev"
    Write-Host ""
    Write-Host "3. O ejecutar con Docker:"
    Write-Host "   npm run docker:up"
    Write-Host ""
    Write-Host "4. Acceder a la aplicación:"
    Write-Host "   Frontend: http://localhost:3000"
    Write-Host "   Backend:  http://localhost:3001"
    Write-Host "   Grafana:  http://localhost:3002 (admin/admin)"
    Write-Host ""
    Write-Host "📚 Documentación disponible en:"
    Write-Host "   - README_PROYECTO.md"
    Write-Host "   - docs\"
    Write-Host ""
    Write-Host "🆘 Para soporte:"
    Write-Host "   - Revisar logs en backend\logs\"
    Write-Host "   - Consultar documentación técnica"
    Write-Host ""
}

# Función principal
function Main {
    Write-Host "==================================================" -ForegroundColor Magenta
    Write-Host "  Sistema de Gestión de Pautas Publicitarias" -ForegroundColor Magenta
    Write-Host "  Setup Script v1.0 (Windows)" -ForegroundColor Magenta
    Write-Host "==================================================" -ForegroundColor Magenta
    Write-Host ""
    
    try {
        Test-Prerequisites
        Install-Dependencies
        Set-Environment
        New-Directories
        Set-Database
        
        if (Test-Setup) {
            Show-NextSteps
            Write-Success "¡Setup completado! 🚀"
        }
        else {
            Write-Error "Setup falló en la verificación final"
            exit 1
        }
    }
    catch {
        Write-Error "Error durante el setup: $($_.Exception.Message)"
        exit 1
    }
}

# Ejecutar función principal
Main
