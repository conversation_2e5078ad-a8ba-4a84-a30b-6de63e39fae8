# Estrategia de Integración - vMix y Dinesat

## Visión General

La integración con vMix y Dinesat es el corazón del sistema, permitiendo el control automático de la emisión de anuncios en televisión y radio respectivamente. Cada plataforma requiere un enfoque específico debido a sus diferentes arquitecturas y métodos de comunicación.

## Integración con vMix (Televisión)

### Arquitectura de Comunicación
vMix expone una API HTTP REST y TCP que permite el control remoto de todas sus funciones. La comunicación se realiza mediante:
- **HTTP API**: Para comandos síncronos y consultas de estado
- **TCP API**: Para comandos de baja latencia y streaming de datos
- **Web Controller**: Interfaz web para monitoreo

### Comandos Principales de vMix

#### 1. Reproducir Video/Anuncio
```http
GET http://localhost:8088/api/?Function=StartInput&Input=C:\Videos\anuncio_cliente1.mp4
```

#### 2. Detener Reproducción
```http
GET http://localhost:8088/api/?Function=StopInput&Input=C:\Videos\anuncio_cliente1.mp4
```

#### 3. Cambiar a Input Específico
```http
GET http://localhost:8088/api/?Function=Cut&Input=3
```

#### 4. Overlay de Anuncio
```http
GET http://localhost:8088/api/?Function=OverlayInput1&Input=C:\Videos\anuncio_overlay.mp4
```

#### 5. Consultar Estado
```http
GET http://localhost:8088/api/
```
Respuesta XML con estado completo del sistema.

### Implementación del Cliente vMix

```typescript
// Servicio de integración vMix
export class VMixService {
  private baseUrl: string;
  private timeout: number;
  
  constructor(config: VMixConfig) {
    this.baseUrl = config.apiUrl || 'http://localhost:8088';
    this.timeout = config.timeout || 5000;
  }

  async playAdvertisement(filePath: string, inputNumber?: number): Promise<VMixResponse> {
    const params = new URLSearchParams({
      Function: 'StartInput',
      Input: filePath
    });
    
    if (inputNumber) {
      params.append('Value', inputNumber.toString());
    }

    try {
      const response = await fetch(`${this.baseUrl}/api/?${params}`, {
        method: 'GET',
        timeout: this.timeout
      });
      
      return this.parseResponse(response);
    } catch (error) {
      throw new VMixError(`Failed to play advertisement: ${error.message}`);
    }
  }

  async getSystemStatus(): Promise<VMixStatus> {
    const response = await fetch(`${this.baseUrl}/api/`);
    const xmlText = await response.text();
    return this.parseXMLStatus(xmlText);
  }

  async preloadInput(filePath: string, inputNumber: number): Promise<void> {
    const params = new URLSearchParams({
      Function: 'AddInput',
      Value: `Video|${filePath}`,
      Input: inputNumber.toString()
    });
    
    await fetch(`${this.baseUrl}/api/?${params}`);
  }
}
```

### Manejo de Errores vMix

```typescript
export class VMixErrorHandler {
  static handleError(error: any): VMixError {
    if (error.code === 'ECONNREFUSED') {
      return new VMixError('vMix no está ejecutándose o no es accesible', 'CONNECTION_REFUSED');
    }
    
    if (error.code === 'ETIMEDOUT') {
      return new VMixError('Timeout al conectar con vMix', 'TIMEOUT');
    }
    
    return new VMixError(`Error desconocido: ${error.message}`, 'UNKNOWN');
  }
}
```

## Integración con Dinesat (Radio)

### Arquitectura de Comunicación
Dinesat no expone una API REST, sino que funciona mediante archivos de intercambio y monitoreo de directorios. La integración se realiza mediante:
- **Archivos de Playlist**: Generación de archivos M3U o TXT
- **Directorio de Intercambio**: Monitoreo de carpeta compartida
- **Archivos de Log**: Lectura de logs para confirmación

### Formatos de Archivo Soportados

#### 1. Formato M3U Extendido
```m3u
#EXTM3U
#EXTINF:30,Anuncio Cliente ABC - Promoción Verano
C:\Audio\Anuncios\cliente_abc_verano.mp3
#EXTINF:15,Jingle Estación
C:\Audio\Jingles\jingle_estacion.mp3
#EXTINF:45,Anuncio Cliente XYZ - Nueva Sucursal
C:\Audio\Anuncios\cliente_xyz_sucursal.mp3
```

#### 2. Formato TXT Simple
```txt
30|C:\Audio\Anuncios\cliente_abc_verano.mp3|Anuncio Cliente ABC
15|C:\Audio\Jingles\jingle_estacion.mp3|Jingle Estación
45|C:\Audio\Anuncios\cliente_xyz_sucursal.mp3|Anuncio Cliente XYZ
```

#### 3. Formato XML Avanzado
```xml
<?xml version="1.0" encoding="UTF-8"?>
<playlist>
  <item>
    <duration>30</duration>
    <file>C:\Audio\Anuncios\cliente_abc_verano.mp3</file>
    <title>Anuncio Cliente ABC - Promoción Verano</title>
    <type>advertisement</type>
    <client_id>ABC001</client_id>
  </item>
</playlist>
```

### Implementación del Generador Dinesat

```typescript
export class DinesatService {
  private outputPath: string;
  private format: 'M3U' | 'TXT' | 'XML';
  
  constructor(config: DinesatConfig) {
    this.outputPath = config.outputPath;
    this.format = config.format || 'M3U';
  }

  async generatePlaylist(schedules: Schedule[], date: Date): Promise<string> {
    const fileName = `pauta_${format(date, 'yyyy-MM-dd')}.${this.format.toLowerCase()}`;
    const filePath = path.join(this.outputPath, fileName);
    
    const content = this.formatPlaylist(schedules);
    
    // Escritura atómica
    const tempPath = `${filePath}.tmp`;
    await fs.writeFile(tempPath, content, 'utf8');
    await fs.rename(tempPath, filePath);
    
    return filePath;
  }

  private formatPlaylist(schedules: Schedule[]): string {
    switch (this.format) {
      case 'M3U':
        return this.formatM3U(schedules);
      case 'TXT':
        return this.formatTXT(schedules);
      case 'XML':
        return this.formatXML(schedules);
      default:
        throw new Error(`Formato no soportado: ${this.format}`);
    }
  }

  private formatM3U(schedules: Schedule[]): string {
    let content = '#EXTM3U\n';
    
    for (const schedule of schedules) {
      content += `#EXTINF:${schedule.duration},${schedule.advertisement.name}\n`;
      content += `${schedule.advertisement.audioFilePath}\n`;
    }
    
    return content;
  }

  async watchDirectory(): Promise<void> {
    const watcher = chokidar.watch(this.outputPath);
    
    watcher.on('change', (filePath) => {
      this.handleFileChange(filePath);
    });
    
    watcher.on('add', (filePath) => {
      this.handleFileAdd(filePath);
    });
  }
}
```

### Monitoreo de Estado Dinesat

```typescript
export class DinesatMonitor {
  private logPath: string;
  private lastPosition: number = 0;
  
  constructor(logPath: string) {
    this.logPath = logPath;
  }

  async checkPlaybackStatus(): Promise<PlaybackStatus[]> {
    const logContent = await fs.readFile(this.logPath, 'utf8');
    const newContent = logContent.slice(this.lastPosition);
    this.lastPosition = logContent.length;
    
    return this.parseLogEntries(newContent);
  }

  private parseLogEntries(content: string): PlaybackStatus[] {
    const lines = content.split('\n').filter(line => line.trim());
    const statuses: PlaybackStatus[] = [];
    
    for (const line of lines) {
      const match = line.match(/(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\s+PLAYED:\s+(.+)/);
      if (match) {
        statuses.push({
          timestamp: new Date(match[1]),
          filePath: match[2],
          status: 'played'
        });
      }
    }
    
    return statuses;
  }
}
```

## Sincronización y Coordinación

### Scheduler Principal

```typescript
export class BroadcastScheduler {
  private vmixService: VMixService;
  private dinesatService: DinesatService;
  private database: Database;
  
  async executeSchedule(schedule: Schedule): Promise<void> {
    try {
      if (schedule.mediumType === 'tv') {
        await this.executeVMixSchedule(schedule);
      } else if (schedule.mediumType === 'radio') {
        await this.executeDinesatSchedule(schedule);
      }
      
      await this.logExecution(schedule, 'success');
    } catch (error) {
      await this.logExecution(schedule, 'failed', error.message);
      throw error;
    }
  }

  private async executeVMixSchedule(schedule: Schedule): Promise<void> {
    const advertisement = schedule.advertisement;
    
    // Pre-cargar el input si es necesario
    await this.vmixService.preloadInput(
      advertisement.videoFilePath, 
      schedule.inputNumber
    );
    
    // Esperar al momento exacto
    await this.waitUntilScheduledTime(schedule.scheduledDateTime);
    
    // Ejecutar comando
    await this.vmixService.playAdvertisement(advertisement.videoFilePath);
  }

  private async executeDinesatSchedule(schedule: Schedule): Promise<void> {
    // Para Dinesat, generamos el archivo de playlist con anticipación
    const schedules = await this.getSchedulesForDate(schedule.scheduledDate, 'radio');
    await this.dinesatService.generatePlaylist(schedules, schedule.scheduledDate);
  }
}
```

## Manejo de Fallos y Recuperación

### Estrategias de Retry

```typescript
export class RetryHandler {
  static async withRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<T> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        if (attempt === maxRetries) {
          throw error;
        }
        
        await new Promise(resolve => setTimeout(resolve, delay * attempt));
      }
    }
    
    throw new Error('Max retries exceeded');
  }
}
```

### Fallbacks y Contingencias

1. **vMix Offline**: 
   - Intentar reconexión automática cada 30 segundos
   - Notificar al operador inmediatamente
   - Registrar fallos para reporte posterior

2. **Dinesat File System Issues**:
   - Verificar permisos de escritura
   - Usar directorio alternativo si está configurado
   - Generar archivo de respaldo en formato alternativo

3. **Network Issues**:
   - Queue de comandos pendientes
   - Sincronización cuando se restaure la conexión
   - Logs detallados para auditoría
