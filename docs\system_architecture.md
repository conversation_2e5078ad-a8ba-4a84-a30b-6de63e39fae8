# Arquitectura del Sistema - Gestión de Pautas Publicitarias

## Visión General

El sistema está diseñado como una aplicación web moderna con arquitectura de microservicios que integra la gestión de pautas publicitarias para televisión (vMix) y radio (Dinesat) en una plataforma unificada.

## Componentes Principales

### 1. Frontend (Cliente Web)
**Tecnología**: React 18 + TypeScript + Vite
**Características**:
- SPA (Single Page Application) responsiva
- Estado global con Zustand
- UI Components con Radix UI + Tailwind CSS
- Calendario/Timeline personalizado para programación
- WebSocket para actualizaciones en tiempo real
- PWA capabilities para acceso offline limitado

### 2. Backend API (Servidor Principal)
**Tecnología**: Node.js + Express + TypeScript
**Características**:
- API REST con documentación OpenAPI/Swagger
- Autenticación JWT con refresh tokens
- Middleware de validación con Zod
- Rate limiting y seguridad con Helmet
- Logging estructurado con Winston
- Health checks y métricas

### 3. Base de Datos
**Tecnología**: PostgreSQL 15
**Características**:
- Esquema relacional optimizado
- Índices para consultas de programación
- Triggers para auditoría automática
- Backup automático programado
- Connection pooling con PgBouncer

### 4. Servicio de Integración vMix
**Tecnología**: Node.js + WebSocket
**Características**:
- Cliente HTTP para vMix Web API
- Monitoreo de estado de conexión
- Queue de comandos con retry automático
- Logging de todas las operaciones
- Fallback para reconexión automática

### 5. Servicio de Integración Dinesat
**Tecnología**: Node.js + File System Watcher
**Características**:
- Generador de archivos de playlist
- Monitoreo de directorio de intercambio
- Validación de formato de archivos
- Backup de archivos generados
- Notificaciones de estado

### 6. Servicio de Programación (Scheduler)
**Tecnología**: Node.js + node-cron
**Características**:
- Ejecución de tareas programadas
- Validación de conflictos de horarios
- Generación automática de comandos
- Alertas de vencimiento de campañas
- Recuperación ante fallos

### 7. Servicio de Reportes
**Tecnología**: Node.js + Puppeteer
**Características**:
- Generación de PDFs automática
- Exportación a múltiples formatos
- Plantillas personalizables
- Envío automático por email
- Almacenamiento en la nube

## Diagrama de Arquitectura

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Mobile App    │    │   Admin Panel   │
│   (React)       │    │   (React Native)│    │   (React)       │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌────────────┴────────────┐
                    │    Load Balancer        │
                    │    (Nginx)              │
                    └────────────┬────────────┘
                                 │
                    ┌────────────┴────────────┐
                    │    API Gateway          │
                    │    (Express)            │
                    └────────────┬────────────┘
                                 │
        ┌────────────────────────┼────────────────────────┐
        │                       │                        │
┌───────┴───────┐    ┌─────────┴─────────┐    ┌─────────┴─────────┐
│   Auth Service │    │   Core API        │    │   File Service    │
│   (JWT)        │    │   (Business Logic)│    │   (Storage)       │
└───────────────┘    └─────────┬─────────┘    └───────────────────┘
                               │
        ┌──────────────────────┼──────────────────────┐
        │                      │                      │
┌───────┴───────┐    ┌─────────┴─────────┐    ┌───────┴───────┐
│   vMix Service │    │   Scheduler       │    │ Dinesat Service│
│   (Integration)│    │   (Cron Jobs)     │    │ (File Gen)    │
└───────┬───────┘    └─────────┬─────────┘    └───────┬───────┘
        │                      │                      │
        │              ┌───────┴───────┐              │
        │              │   Database    │              │
        │              │  (PostgreSQL) │              │
        │              └───────────────┘              │
        │                                             │
┌───────┴───────┐                            ┌───────┴───────┐
│   vMix Server │                            │ Dinesat Files │
│   (External)  │                            │ (File System) │
└───────────────┘                            └───────────────┘
```

## Stack Tecnológico Recomendado

### Backend
- **Runtime**: Node.js 20 LTS
- **Framework**: Express.js 4.18
- **Lenguaje**: TypeScript 5.0
- **Base de Datos**: PostgreSQL 15
- **ORM**: Prisma 5.0
- **Validación**: Zod
- **Testing**: Jest + Supertest

### Frontend
- **Framework**: React 18
- **Lenguaje**: TypeScript 5.0
- **Build Tool**: Vite 4.0
- **Estado**: Zustand
- **UI Library**: Radix UI + Tailwind CSS
- **Calendario**: React Big Calendar (customizado)
- **Testing**: Vitest + Testing Library

### DevOps & Infraestructura
- **Containerización**: Docker + Docker Compose
- **Reverse Proxy**: Nginx
- **Monitoreo**: Prometheus + Grafana
- **Logs**: Winston + ELK Stack
- **CI/CD**: GitHub Actions
- **Deployment**: Docker Swarm o Kubernetes

## Justificación de Tecnologías

### ¿Por qué Node.js?
1. **Ecosistema unificado**: JavaScript/TypeScript en frontend y backend
2. **Excelente para I/O**: Ideal para integraciones con APIs externas
3. **Comunidad activa**: Amplio soporte para librerías de broadcast
4. **Desarrollo rápido**: Prototipado y desarrollo ágil
5. **Escalabilidad**: Microservicios fáciles de escalar

### ¿Por qué PostgreSQL?
1. **Confiabilidad**: ACID compliance para datos críticos
2. **Características avanzadas**: JSON support, triggers, stored procedures
3. **Rendimiento**: Excelente para consultas complejas de programación
4. **Extensibilidad**: Soporte para extensiones como TimescaleDB
5. **Comunidad**: Amplio soporte y documentación

### ¿Por qué React?
1. **Ecosistema maduro**: Amplia variedad de componentes y librerías
2. **Rendimiento**: Virtual DOM y optimizaciones automáticas
3. **Flexibilidad**: Fácil personalización de componentes
4. **Comunidad**: Gran cantidad de desarrolladores disponibles
5. **Tooling**: Excelentes herramientas de desarrollo

## Patrones de Diseño Implementados

### 1. Repository Pattern
- Abstracción de acceso a datos
- Facilita testing con mocks
- Separación de lógica de negocio

### 2. Service Layer Pattern
- Encapsulación de lógica de negocio
- Reutilización entre controladores
- Transacciones y validaciones centralizadas

### 3. Observer Pattern
- Notificaciones en tiempo real
- Eventos de sistema desacoplados
- WebSocket para actualizaciones live

### 4. Command Pattern
- Queue de comandos para vMix/Dinesat
- Retry automático y rollback
- Logging detallado de operaciones

### 5. Factory Pattern
- Creación de reportes dinámicos
- Generación de archivos por tipo
- Configuración flexible de integraciones
