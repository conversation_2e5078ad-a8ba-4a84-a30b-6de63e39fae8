import cron from 'node-cron';
import { PrismaClient } from '@prisma/client';
import { logger } from '@/utils/logger';
import { VMixService } from './VMixService';
import { DinesatService } from './DinesatService';
import { WebSocketService } from './WebSocketService';
import { format, addDays, startOfDay, endOfDay } from 'date-fns';

const prisma = new PrismaClient();

export interface ScheduledTask {
  id: string;
  scheduleId: string;
  executionTime: Date;
  mediumType: 'TV' | 'RADIO';
  task: cron.ScheduledTask;
}

export class SchedulerService {
  private tasks: Map<string, ScheduledTask> = new Map();
  private isRunning: boolean = false;
  private dailyGenerationTask: cron.ScheduledTask | null = null;

  constructor(
    private vmixService: VMixService,
    private dinesatService: DinesatService,
    private webSocketService: WebSocketService
  ) {
    // Listen to Dinesat events
    this.dinesatService.on('playlist:generated', (data) => {
      this.webSocketService.broadcast('dinesat:playlist:generated', data);
    });
  }

  /**
   * Start the scheduler service
   */
  start(): void {
    if (this.isRunning) {
      logger.warn('Scheduler service is already running');
      return;
    }

    this.isRunning = true;
    logger.info('🕐 Starting Scheduler Service');

    // Load existing schedules
    this.loadSchedules();

    // Schedule daily playlist generation for Dinesat (every day at 23:45)
    this.dailyGenerationTask = cron.schedule('45 23 * * *', () => {
      this.generateDailyPlaylists();
    });

    // Schedule cleanup of old logs (every day at 02:00)
    cron.schedule('0 2 * * *', () => {
      this.cleanupOldLogs();
    });

    logger.info('✅ Scheduler Service started successfully');
  }

  /**
   * Stop the scheduler service
   */
  stop(): void {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;
    logger.info('🛑 Stopping Scheduler Service');

    // Stop all scheduled tasks
    for (const [taskId, scheduledTask] of this.tasks) {
      scheduledTask.task.stop();
      scheduledTask.task.destroy();
    }
    this.tasks.clear();

    // Stop daily generation task
    if (this.dailyGenerationTask) {
      this.dailyGenerationTask.stop();
      this.dailyGenerationTask.destroy();
      this.dailyGenerationTask = null;
    }

    logger.info('✅ Scheduler Service stopped');
  }

  /**
   * Add a new schedule
   */
  async addSchedule(scheduleId: string): Promise<void> {
    try {
      const schedule = await prisma.schedule.findUnique({
        where: { id: scheduleId },
        include: {
          advertisement: {
            include: {
              client: true
            }
          }
        }
      });

      if (!schedule) {
        throw new Error(`Schedule not found: ${scheduleId}`);
      }

      if (schedule.status !== 'SCHEDULED') {
        logger.warn(`Schedule ${scheduleId} is not in SCHEDULED status`);
        return;
      }

      // Create execution datetime
      const executionDateTime = new Date(schedule.scheduledDate);
      const [hours, minutes, seconds] = schedule.scheduledTime.toISOString().split('T')[1].split(':');
      executionDateTime.setHours(parseInt(hours), parseInt(minutes), parseInt(seconds.split('.')[0]));

      // Skip if execution time is in the past
      if (executionDateTime < new Date()) {
        logger.warn(`Schedule ${scheduleId} execution time is in the past`);
        return;
      }

      // Create cron expression for the specific datetime
      const cronExpression = this.createCronExpression(executionDateTime);

      // Create scheduled task
      const task = cron.schedule(cronExpression, async () => {
        await this.executeSchedule(schedule);
        // Remove task after execution
        this.removeSchedule(scheduleId);
      }, {
        scheduled: false
      });

      // Store the task
      const scheduledTask: ScheduledTask = {
        id: `${scheduleId}_${Date.now()}`,
        scheduleId,
        executionTime: executionDateTime,
        mediumType: schedule.mediumType,
        task
      };

      this.tasks.set(scheduledTask.id, scheduledTask);
      task.start();

      logger.info(`Scheduled task added: ${scheduleId} at ${executionDateTime.toISOString()}`);
      
      // Notify via WebSocket
      this.webSocketService.broadcast('schedule:added', {
        scheduleId,
        executionTime: executionDateTime,
        mediumType: schedule.mediumType
      });

    } catch (error) {
      logger.error(`Error adding schedule ${scheduleId}:`, error);
      throw error;
    }
  }

  /**
   * Remove a schedule
   */
  removeSchedule(scheduleId: string): void {
    const tasksToRemove = Array.from(this.tasks.entries())
      .filter(([_, task]) => task.scheduleId === scheduleId);

    for (const [taskId, scheduledTask] of tasksToRemove) {
      scheduledTask.task.stop();
      scheduledTask.task.destroy();
      this.tasks.delete(taskId);
      
      logger.info(`Scheduled task removed: ${scheduleId}`);
    }

    // Notify via WebSocket
    this.webSocketService.broadcast('schedule:removed', { scheduleId });
  }

  /**
   * Execute a schedule
   */
  private async executeSchedule(schedule: any): Promise<void> {
    const startTime = new Date();
    logger.info(`Executing schedule: ${schedule.id} (${schedule.mediumType})`);

    try {
      // Update schedule status to executing
      await prisma.schedule.update({
        where: { id: schedule.id },
        data: { status: 'EXECUTED' }
      });

      let result: any;

      if (schedule.mediumType === 'TV') {
        // Execute vMix command
        result = await this.vmixService.playAdvertisement(
          schedule.advertisement.videoFilePath,
          undefined // Let vMix find a free input
        );
      } else if (schedule.mediumType === 'RADIO') {
        // For radio, the playlist should already be generated
        // We just log the execution
        result = { success: true, message: 'Radio playlist already generated' };
      }

      // Log successful execution
      await prisma.emissionLog.create({
        data: {
          scheduleId: schedule.id,
          advertisementId: schedule.advertisementId,
          clientId: schedule.advertisement.clientId,
          mediumType: schedule.mediumType,
          scheduledDatetime: new Date(schedule.scheduledDate.getTime() + 
            new Date(`1970-01-01T${schedule.scheduledTime}`).getTime()),
          executedDatetime: new Date(),
          status: 'CONFIRMED',
          vmixResponse: schedule.mediumType === 'TV' ? result : null
        }
      });

      const executionTime = new Date().getTime() - startTime.getTime();
      logger.info(`Schedule executed successfully: ${schedule.id} (${executionTime}ms)`);

      // Notify via WebSocket
      this.webSocketService.broadcast('schedule:executed', {
        scheduleId: schedule.id,
        mediumType: schedule.mediumType,
        executionTime,
        success: true
      });

    } catch (error) {
      logger.error(`Error executing schedule ${schedule.id}:`, error);

      // Log failed execution
      await prisma.emissionLog.create({
        data: {
          scheduleId: schedule.id,
          advertisementId: schedule.advertisementId,
          clientId: schedule.advertisement.clientId,
          mediumType: schedule.mediumType,
          scheduledDatetime: new Date(schedule.scheduledDate.getTime() + 
            new Date(`1970-01-01T${schedule.scheduledTime}`).getTime()),
          status: 'FAILED',
          errorMessage: error.message
        }
      });

      // Update schedule status to failed
      await prisma.schedule.update({
        where: { id: schedule.id },
        data: { status: 'FAILED' }
      });

      // Notify via WebSocket
      this.webSocketService.broadcast('schedule:failed', {
        scheduleId: schedule.id,
        mediumType: schedule.mediumType,
        error: error.message
      });
    }
  }

  /**
   * Load existing schedules from database
   */
  private async loadSchedules(): Promise<void> {
    try {
      const tomorrow = addDays(new Date(), 1);
      const schedules = await prisma.schedule.findMany({
        where: {
          status: 'SCHEDULED',
          scheduledDate: {
            gte: startOfDay(new Date()),
            lte: endOfDay(tomorrow)
          }
        },
        include: {
          advertisement: {
            include: {
              client: true
            }
          }
        }
      });

      logger.info(`Loading ${schedules.length} existing schedules`);

      for (const schedule of schedules) {
        await this.addSchedule(schedule.id);
      }

      logger.info(`✅ Loaded ${schedules.length} schedules`);
    } catch (error) {
      logger.error('Error loading schedules:', error);
    }
  }

  /**
   * Generate daily playlists for Dinesat
   */
  private async generateDailyPlaylists(): Promise<void> {
    try {
      const tomorrow = addDays(new Date(), 1);
      
      logger.info(`Generating Dinesat playlist for ${format(tomorrow, 'yyyy-MM-dd')}`);

      // Get radio schedules for tomorrow
      const radioSchedules = await prisma.schedule.findMany({
        where: {
          mediumType: 'RADIO',
          status: 'SCHEDULED',
          scheduledDate: {
            gte: startOfDay(tomorrow),
            lte: endOfDay(tomorrow)
          }
        },
        include: {
          advertisement: {
            include: {
              client: true
            }
          }
        },
        orderBy: {
          scheduledTime: 'asc'
        }
      });

      if (radioSchedules.length === 0) {
        logger.info('No radio schedules found for tomorrow');
        return;
      }

      // Convert to Dinesat format
      const dinesatSchedules = radioSchedules.map(schedule => ({
        id: schedule.id,
        scheduledTime: new Date(`${format(tomorrow, 'yyyy-MM-dd')}T${schedule.scheduledTime}`),
        duration: schedule.durationSeconds,
        advertisement: {
          id: schedule.advertisement.id,
          name: schedule.advertisement.name,
          code: schedule.advertisement.code,
          audioFilePath: schedule.advertisement.audioFilePath!,
          client: {
            name: schedule.advertisement.client.name
          }
        }
      }));

      // Generate playlist
      const playlistPath = await this.dinesatService.generatePlaylist(dinesatSchedules, tomorrow);
      
      logger.info(`✅ Dinesat playlist generated: ${playlistPath}`);

    } catch (error) {
      logger.error('Error generating daily playlists:', error);
    }
  }

  /**
   * Clean up old emission logs
   */
  private async cleanupOldLogs(): Promise<void> {
    try {
      const cutoffDate = addDays(new Date(), -90); // Keep logs for 90 days

      const result = await prisma.emissionLog.deleteMany({
        where: {
          createdAt: {
            lt: cutoffDate
          }
        }
      });

      logger.info(`Cleaned up ${result.count} old emission logs`);
    } catch (error) {
      logger.error('Error cleaning up old logs:', error);
    }
  }

  /**
   * Create cron expression for specific datetime
   */
  private createCronExpression(date: Date): string {
    const minute = date.getMinutes();
    const hour = date.getHours();
    const day = date.getDate();
    const month = date.getMonth() + 1;
    
    return `${minute} ${hour} ${day} ${month} *`;
  }

  /**
   * Get current scheduler status
   */
  getStatus(): {
    isRunning: boolean;
    activeTasks: number;
    nextExecutions: Array<{
      scheduleId: string;
      executionTime: Date;
      mediumType: string;
    }>;
  } {
    const nextExecutions = Array.from(this.tasks.values())
      .sort((a, b) => a.executionTime.getTime() - b.executionTime.getTime())
      .slice(0, 10)
      .map(task => ({
        scheduleId: task.scheduleId,
        executionTime: task.executionTime,
        mediumType: task.mediumType
      }));

    return {
      isRunning: this.isRunning,
      activeTasks: this.tasks.size,
      nextExecutions
    };
  }
}
