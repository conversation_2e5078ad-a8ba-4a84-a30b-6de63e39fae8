import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from 'react-query';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';

// Layout
import { Layout } from '@/components/layout/Layout';
import { AuthGuard } from '@/components/auth/AuthGuard';

// Pages
import { LoginPage } from '@/pages/auth/LoginPage';
import { DashboardPage } from '@/pages/dashboard/DashboardPage';
import { ClientsPage } from '@/pages/clients/ClientsPage';
import { AdvertisementsPage } from '@/pages/advertisements/AdvertisementsPage';
import { SchedulePage } from '@/pages/schedule/SchedulePage';
import { ReportsPage } from '@/pages/reports/ReportsPage';
import { SystemPage } from '@/pages/system/SystemPage';

// Services
import { WebSocketProvider } from '@/services/WebSocketService';
import { ToastProvider } from '@/components/ui/toast';

// Styles
import './index.css';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 3,
      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
    },
  },
});

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <DndProvider backend={HTML5Backend}>
        <WebSocketProvider>
          <ToastProvider>
            <Router>
              <div className="min-h-screen bg-studio-black text-studio-light">
                <Routes>
                  {/* Public routes */}
                  <Route path="/login" element={<LoginPage />} />
                  
                  {/* Protected routes */}
                  <Route path="/" element={<AuthGuard><Layout /></AuthGuard>}>
                    <Route index element={<Navigate to="/dashboard" replace />} />
                    <Route path="dashboard" element={<DashboardPage />} />
                    <Route path="clients" element={<ClientsPage />} />
                    <Route path="advertisements" element={<AdvertisementsPage />} />
                    <Route path="schedule" element={<SchedulePage />} />
                    <Route path="reports" element={<ReportsPage />} />
                    <Route path="system" element={<SystemPage />} />
                  </Route>
                  
                  {/* Catch all route */}
                  <Route path="*" element={<Navigate to="/dashboard" replace />} />
                </Routes>
              </div>
            </Router>
          </ToastProvider>
        </WebSocketProvider>
      </DndProvider>
    </QueryClientProvider>
  );
}

export default App;
