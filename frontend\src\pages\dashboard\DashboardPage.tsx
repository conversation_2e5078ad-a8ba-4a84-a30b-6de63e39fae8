import React from 'react';
import { useQuery } from 'react-query';
import { 
  Tv, 
  Radio, 
  BarChart3, 
  AlertTriangle, 
  Clock, 
  Users, 
  TrendingUp,
  Activity
} from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { StatusIndicator } from '@/components/ui/status-indicator';
import { TimelineWidget } from '@/components/dashboard/TimelineWidget';
import { AlertsWidget } from '@/components/dashboard/AlertsWidget';
import { MetricsWidget } from '@/components/dashboard/MetricsWidget';
import { NextSchedulesWidget } from '@/components/dashboard/NextSchedulesWidget';

import { dashboardService } from '@/services/dashboardService';
import { useWebSocket } from '@/hooks/useWebSocket';

export const DashboardPage: React.FC = () => {
  // Fetch dashboard data
  const { data: dashboardData, isLoading } = useQuery(
    'dashboard',
    dashboardService.getDashboardData,
    {
      refetchInterval: 30000, // Refresh every 30 seconds
    }
  );

  // WebSocket for real-time updates
  const { isConnected } = useWebSocket();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="loading-spinner" />
        <span className="ml-2 text-studio-light/70">Cargando dashboard...</span>
      </div>
    );
  }

  const {
    systemStatus,
    todayMetrics,
    nextSchedules,
    alerts,
    timelineData
  } = dashboardData || {};

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="page-header">
        <div>
          <h1 className="page-title">Control Room</h1>
          <p className="text-studio-light/70 mt-1">
            Vista general del sistema de pautas publicitarias
          </p>
        </div>
        <div className="flex items-center gap-4">
          <StatusIndicator 
            status={isConnected ? 'online' : 'offline'}
            label={isConnected ? 'Conectado' : 'Desconectado'}
            pulse={isConnected}
          />
          <div className="text-sm text-studio-light/70">
            Última actualización: {new Date().toLocaleTimeString()}
          </div>
        </div>
      </div>

      {/* System Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* TV Status */}
        <Card className="broadcast-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-studio-light/70">
              📺 TELEVISIÓN
            </CardTitle>
            <Tv className="h-4 w-4 text-broadcast-blue" />
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">Estado vMix:</span>
                <StatusIndicator 
                  status={systemStatus?.vmix?.online ? 'online' : 'offline'}
                  label={systemStatus?.vmix?.online ? 'Online' : 'Offline'}
                />
              </div>
              <div className="space-y-1">
                <div className="text-sm text-studio-light/70">Próximo:</div>
                <div className="font-medium">
                  {nextSchedules?.tv?.time || '--:--'}
                </div>
                <div className="text-sm text-broadcast-blue">
                  {nextSchedules?.tv?.client || 'Sin programación'}
                </div>
              </div>
              <div className="w-full bg-studio-panel rounded-full h-2">
                <div 
                  className="bg-broadcast-blue h-2 rounded-full transition-all duration-300"
                  style={{ width: `${nextSchedules?.tv?.progress || 0}%` }}
                />
              </div>
              <div className="text-xs text-studio-light/60">
                {nextSchedules?.tv?.progress || 0}% del día completado
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Radio Status */}
        <Card className="broadcast-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-studio-light/70">
              📻 RADIO
            </CardTitle>
            <Radio className="h-4 w-4 text-frequency-purple" />
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">Estado Dinesat:</span>
                <StatusIndicator 
                  status={systemStatus?.dinesat?.online ? 'online' : 'offline'}
                  label={systemStatus?.dinesat?.online ? 'Online' : 'Offline'}
                />
              </div>
              <div className="space-y-1">
                <div className="text-sm text-studio-light/70">Próximo:</div>
                <div className="font-medium">
                  {nextSchedules?.radio?.time || '--:--'}
                </div>
                <div className="text-sm text-frequency-purple">
                  {nextSchedules?.radio?.client || 'Sin programación'}
                </div>
              </div>
              <div className="w-full bg-studio-panel rounded-full h-2">
                <div 
                  className="bg-frequency-purple h-2 rounded-full transition-all duration-300"
                  style={{ width: `${nextSchedules?.radio?.progress || 0}%` }}
                />
              </div>
              <div className="text-xs text-studio-light/60">
                {nextSchedules?.radio?.progress || 0}% del día completado
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Summary */}
        <Card className="broadcast-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-studio-light/70">
              📊 RESUMEN
            </CardTitle>
            <BarChart3 className="h-4 w-4 text-broadcast-green" />
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <div className="metric-value">
                    {todayMetrics?.totalAds || 0}
                  </div>
                  <div className="metric-label">Hoy</div>
                </div>
                <div className="text-center">
                  <div className="metric-value">
                    {todayMetrics?.activeClients || 0}
                  </div>
                  <div className="metric-label">Activos</div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <div className="text-lg font-bold text-broadcast-green">
                    {todayMetrics?.successRate || 0}%
                  </div>
                  <div className="metric-label">Éxito</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-broadcast-red">
                    {todayMetrics?.failures || 0}
                  </div>
                  <div className="metric-label">Fallos</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Timeline */}
      <Card className="broadcast-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5 text-broadcast-blue" />
            🗓️ TIMELINE HOY
          </CardTitle>
        </CardHeader>
        <CardContent>
          <TimelineWidget data={timelineData} />
        </CardContent>
      </Card>

      {/* Bottom Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Alerts */}
        <Card className="broadcast-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-broadcast-amber" />
              🚨 ALERTAS Y NOTIFICACIONES
            </CardTitle>
          </CardHeader>
          <CardContent>
            <AlertsWidget alerts={alerts} />
          </CardContent>
        </Card>

        {/* Next Schedules */}
        <Card className="broadcast-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-broadcast-green" />
              ⏰ PRÓXIMAS EMISIONES
            </CardTitle>
          </CardHeader>
          <CardContent>
            <NextSchedulesWidget schedules={nextSchedules?.upcoming} />
          </CardContent>
        </Card>
      </div>

      {/* Quick Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <MetricsWidget
          title="Anuncios Hoy"
          value={todayMetrics?.totalAds || 0}
          icon={<TrendingUp className="h-4 w-4" />}
          trend={todayMetrics?.adsTrend}
          color="blue"
        />
        <MetricsWidget
          title="Clientes Activos"
          value={todayMetrics?.activeClients || 0}
          icon={<Users className="h-4 w-4" />}
          trend={todayMetrics?.clientsTrend}
          color="green"
        />
        <MetricsWidget
          title="Tasa de Éxito"
          value={`${todayMetrics?.successRate || 0}%`}
          icon={<BarChart3 className="h-4 w-4" />}
          trend={todayMetrics?.successTrend}
          color="green"
        />
        <MetricsWidget
          title="Tiempo Promedio"
          value={`${todayMetrics?.avgExecutionTime || 0}ms`}
          icon={<Clock className="h-4 w-4" />}
          trend={todayMetrics?.timeTrend}
          color="purple"
        />
      </div>
    </div>
  );
};
