# Database
DATABASE_URL="postgresql://pautas_user:pautas_password@localhost:5432/pautas_publicitarias"

# Redis
REDIS_URL="redis://localhost:6379"

# JWT
JWT_SECRET="your-super-secret-jwt-key-change-in-production"
JWT_EXPIRES_IN="24h"
JWT_REFRESH_EXPIRES_IN="7d"

# Server
NODE_ENV="development"
PORT=3001
FRONTEND_URL="http://localhost:3000"

# vMix Integration
VMIX_API_URL="http://localhost:8088"
VMIX_TIMEOUT=5000
VMIX_RETRY_ATTEMPTS=3
VMIX_PASSWORD=""

# Dinesat Integration
DINESAT_OUTPUT_PATH="./dinesat-output"
DINESAT_FORMAT="M3U"
DINESAT_LOG_PATH="./dinesat-logs/playback.log"
DINESAT_WATCH_DIRECTORY=true

# File Storage
MEDIA_STORAGE_PATH="./media"
MAX_FILE_SIZE="100MB"
ALLOWED_VIDEO_FORMATS="mp4,avi,mov,wmv"
ALLOWED_AUDIO_FORMATS="mp3,wav,aac,ogg"

# Email (for reports)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"
EMAIL_FROM="<EMAIL>"

# Logging
LOG_LEVEL="info"
LOG_FILE="./logs/app.log"
LOG_MAX_SIZE="10MB"
LOG_MAX_FILES=5

# Monitoring
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=9090

# Security
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
CORS_ORIGIN="http://localhost:3000"

# Backup
BACKUP_ENABLED=true
BACKUP_RETENTION_DAYS=30
BACKUP_PATH="./backups"

# System Configuration
MAX_SCHEDULE_DAYS_AHEAD=90
CLEANUP_OLD_LOGS_DAYS=90
PLAYLIST_GENERATION_TIME="23:45"
