# Ejemplos de Integración Dinesat - Formatos y Archivos

## Formatos de Archivo Soportados por Dinesat

### 1. Formato M3U Extendido (Recomendado)

#### Estructura Básica
```m3u
#EXTM3U
#EXTINF:duración_en_segundos,Título del archivo
ruta_completa_del_archivo.mp3
```

#### Ejemplo Completo de Pauta Diaria
```m3u
#EXTM3U
#PLAYLIST:Pauta Publicitaria - 2024-06-15

# Bloque Mañana - 06:00
#EXTINF:30,Anuncio Supermercado Los Andes - Ofertas Semanales
C:\Audio\Anuncios\supermercado_losandes_ofertas.mp3

#EXTINF:15,Jingle Estación Radio Éxito
C:\Audio\Jingles\radio_exito_jingle_corto.mp3

#EXTINF:45,Anuncio Banco Nacional - Créditos Hipotecarios
C:\Audio\Anuncios\banco_nacional_hipotecarios.mp3

# Bloque Mediodía - 12:00
#EXTINF:20,Anuncio Restaurante El Buen Sabor
C:\Audio\Anuncios\restaurante_buensabor_almuerzo.mp3

#EXTINF:30,Anuncio Concesionario Toyota - Nuevos Modelos
C:\Audio\Anuncios\toyota_nuevos_modelos.mp3

# Bloque Tarde - 18:00
#EXTINF:25,Anuncio Farmacia San José - Promociones
C:\Audio\Anuncios\farmacia_sanjose_promociones.mp3

#EXTINF:35,Anuncio Universidad Técnica - Inscripciones Abiertas
C:\Audio\Anuncios\universidad_tecnica_inscripciones.mp3
```

### 2. Formato TXT Delimitado

#### Estructura
```
duración|ruta_archivo|título|cliente|código_anuncio|hora_programada
```

#### Ejemplo
```txt
30|C:\Audio\Anuncios\supermercado_ofertas.mp3|Ofertas Semanales|Supermercado Los Andes|SA001|06:00:00
15|C:\Audio\Jingles\radio_exito_jingle.mp3|Jingle Estación|Radio Éxito|RE001|06:00:30
45|C:\Audio\Anuncios\banco_hipotecarios.mp3|Créditos Hipotecarios|Banco Nacional|BN001|06:00:45
20|C:\Audio\Anuncios\restaurante_almuerzo.mp3|Menú del Día|Restaurante El Buen Sabor|RBS001|12:00:00
30|C:\Audio\Anuncios\toyota_modelos.mp3|Nuevos Modelos 2024|Concesionario Toyota|CT001|12:00:20
25|C:\Audio\Anuncios\farmacia_promociones.mp3|Promociones Junio|Farmacia San José|FSJ001|18:00:00
35|C:\Audio\Anuncios\universidad_inscripciones.mp3|Inscripciones Abiertas|Universidad Técnica|UT001|18:00:25
```

### 3. Formato XML Avanzado

#### Estructura Completa
```xml
<?xml version="1.0" encoding="UTF-8"?>
<playlist>
  <metadata>
    <date>2024-06-15</date>
    <station>Radio Éxito FM</station>
    <generated_by>Sistema de Gestión de Pautas</generated_by>
    <generated_at>2024-06-14T23:45:00Z</generated_at>
  </metadata>
  
  <items>
    <item>
      <id>1</id>
      <scheduled_time>06:00:00</scheduled_time>
      <duration>30</duration>
      <file>C:\Audio\Anuncios\supermercado_ofertas.mp3</file>
      <title>Ofertas Semanales</title>
      <client>
        <name>Supermercado Los Andes</name>
        <code>SA001</code>
        <campaign>Ofertas Junio 2024</campaign>
      </client>
      <type>advertisement</type>
      <priority>high</priority>
    </item>
    
    <item>
      <id>2</id>
      <scheduled_time>06:00:30</scheduled_time>
      <duration>15</duration>
      <file>C:\Audio\Jingles\radio_exito_jingle.mp3</file>
      <title>Jingle Estación</title>
      <client>
        <name>Radio Éxito</name>
        <code>RE001</code>
      </client>
      <type>jingle</type>
      <priority>medium</priority>
    </item>
  </items>
</playlist>
```

## Implementación del Generador de Archivos

### Generador M3U
```typescript
export class M3UGenerator {
  generatePlaylist(schedules: Schedule[], date: Date): string {
    let content = '#EXTM3U\n';
    content += `#PLAYLIST:Pauta Publicitaria - ${format(date, 'yyyy-MM-dd')}\n\n`;
    
    // Agrupar por bloques horarios
    const blocks = this.groupByTimeBlocks(schedules);
    
    for (const [blockName, blockSchedules] of blocks) {
      content += `# ${blockName}\n`;
      
      for (const schedule of blockSchedules) {
        const ad = schedule.advertisement;
        content += `#EXTINF:${ad.duration},${ad.name}\n`;
        content += `${ad.audioFilePath}\n\n`;
      }
    }
    
    return content;
  }
  
  private groupByTimeBlocks(schedules: Schedule[]): Map<string, Schedule[]> {
    const blocks = new Map<string, Schedule[]>();
    
    for (const schedule of schedules) {
      const hour = schedule.scheduledTime.getHours();
      let blockName: string;
      
      if (hour >= 6 && hour < 12) {
        blockName = 'Bloque Mañana - 06:00-12:00';
      } else if (hour >= 12 && hour < 18) {
        blockName = 'Bloque Tarde - 12:00-18:00';
      } else {
        blockName = 'Bloque Noche - 18:00-24:00';
      }
      
      if (!blocks.has(blockName)) {
        blocks.set(blockName, []);
      }
      blocks.get(blockName)!.push(schedule);
    }
    
    return blocks;
  }
}
```

### Generador TXT
```typescript
export class TXTGenerator {
  generatePlaylist(schedules: Schedule[], date: Date): string {
    let content = '';
    
    // Ordenar por hora programada
    const sortedSchedules = schedules.sort((a, b) => 
      a.scheduledTime.getTime() - b.scheduledTime.getTime()
    );
    
    for (const schedule of sortedSchedules) {
      const ad = schedule.advertisement;
      const timeStr = format(schedule.scheduledTime, 'HH:mm:ss');
      
      const line = [
        ad.duration.toString(),
        ad.audioFilePath,
        ad.name,
        ad.client.name,
        ad.code,
        timeStr
      ].join('|');
      
      content += line + '\n';
    }
    
    return content;
  }
}
```

### Generador XML
```typescript
export class XMLGenerator {
  generatePlaylist(schedules: Schedule[], date: Date): string {
    const doc = document.implementation.createDocument('', '', null);
    const playlist = doc.createElement('playlist');
    doc.appendChild(playlist);
    
    // Metadata
    const metadata = doc.createElement('metadata');
    this.addElement(doc, metadata, 'date', format(date, 'yyyy-MM-dd'));
    this.addElement(doc, metadata, 'station', 'Radio Éxito FM');
    this.addElement(doc, metadata, 'generated_by', 'Sistema de Gestión de Pautas');
    this.addElement(doc, metadata, 'generated_at', new Date().toISOString());
    playlist.appendChild(metadata);
    
    // Items
    const items = doc.createElement('items');
    
    schedules.forEach((schedule, index) => {
      const item = doc.createElement('item');
      const ad = schedule.advertisement;
      
      this.addElement(doc, item, 'id', (index + 1).toString());
      this.addElement(doc, item, 'scheduled_time', format(schedule.scheduledTime, 'HH:mm:ss'));
      this.addElement(doc, item, 'duration', ad.duration.toString());
      this.addElement(doc, item, 'file', ad.audioFilePath);
      this.addElement(doc, item, 'title', ad.name);
      
      // Cliente
      const client = doc.createElement('client');
      this.addElement(doc, client, 'name', ad.client.name);
      this.addElement(doc, client, 'code', ad.code);
      item.appendChild(client);
      
      this.addElement(doc, item, 'type', 'advertisement');
      this.addElement(doc, item, 'priority', 'high');
      
      items.appendChild(item);
    });
    
    playlist.appendChild(items);
    
    return new XMLSerializer().serializeToString(doc);
  }
  
  private addElement(doc: Document, parent: Element, name: string, value: string): void {
    const element = doc.createElement(name);
    element.textContent = value;
    parent.appendChild(element);
  }
}
```

## Monitoreo de Archivos y Logs

### Monitor de Directorio
```typescript
export class DinesatDirectoryMonitor {
  private watcher: FSWatcher | null = null;
  private outputPath: string;
  
  constructor(outputPath: string) {
    this.outputPath = outputPath;
  }
  
  startWatching(): void {
    this.watcher = chokidar.watch(this.outputPath, {
      ignored: /^\./, // Ignorar archivos ocultos
      persistent: true,
      ignoreInitial: true
    });
    
    this.watcher
      .on('add', (path) => this.handleFileAdded(path))
      .on('change', (path) => this.handleFileChanged(path))
      .on('unlink', (path) => this.handleFileDeleted(path))
      .on('error', (error) => this.handleError(error));
  }
  
  stopWatching(): void {
    if (this.watcher) {
      this.watcher.close();
      this.watcher = null;
    }
  }
  
  private handleFileAdded(filePath: string): void {
    console.log(`Archivo agregado: ${filePath}`);
    EventBus.emit('dinesat:file:added', { filePath });
  }
  
  private handleFileChanged(filePath: string): void {
    console.log(`Archivo modificado: ${filePath}`);
    EventBus.emit('dinesat:file:changed', { filePath });
  }
  
  private handleFileDeleted(filePath: string): void {
    console.log(`Archivo eliminado: ${filePath}`);
    EventBus.emit('dinesat:file:deleted', { filePath });
  }
  
  private handleError(error: Error): void {
    console.error('Error en monitoreo de directorio:', error);
    EventBus.emit('dinesat:monitor:error', { error });
  }
}
```

### Parser de Logs de Dinesat
```typescript
export class DinesatLogParser {
  private logPath: string;
  private lastPosition: number = 0;
  
  constructor(logPath: string) {
    this.logPath = logPath;
  }
  
  async parseNewEntries(): Promise<DinesatLogEntry[]> {
    try {
      const stats = await fs.stat(this.logPath);
      
      if (stats.size <= this.lastPosition) {
        return []; // No hay nuevas entradas
      }
      
      const stream = fs.createReadStream(this.logPath, {
        start: this.lastPosition,
        encoding: 'utf8'
      });
      
      const entries: DinesatLogEntry[] = [];
      const rl = readline.createInterface({ input: stream });
      
      for await (const line of rl) {
        const entry = this.parseLine(line);
        if (entry) {
          entries.push(entry);
        }
      }
      
      this.lastPosition = stats.size;
      return entries;
      
    } catch (error) {
      throw new Error(`Error parsing Dinesat log: ${error.message}`);
    }
  }
  
  private parseLine(line: string): DinesatLogEntry | null {
    // Formato típico: "2024-06-15 14:30:25 [PLAYED] C:\Audio\Anuncios\cliente_abc.mp3 (30s)"
    const regex = /(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) \[(\w+)\] (.+) \((\d+)s\)/;
    const match = line.match(regex);
    
    if (!match) return null;
    
    return {
      timestamp: new Date(match[1]),
      action: match[2] as 'PLAYED' | 'SKIPPED' | 'ERROR',
      filePath: match[3],
      duration: parseInt(match[4]),
      rawLine: line
    };
  }
}

interface DinesatLogEntry {
  timestamp: Date;
  action: 'PLAYED' | 'SKIPPED' | 'ERROR';
  filePath: string;
  duration: number;
  rawLine: string;
}
```

## Configuración y Rutas

### Configuración Típica de Dinesat
```typescript
interface DinesatConfig {
  // Rutas de archivos
  playlistOutputPath: string;
  audioFilesPath: string;
  logFilePath: string;
  backupPath: string;
  
  // Formatos soportados
  supportedFormats: ('M3U' | 'TXT' | 'XML')[];
  defaultFormat: 'M3U' | 'TXT' | 'XML';
  
  // Configuración de archivos
  fileEncoding: 'utf8' | 'latin1';
  lineEnding: '\n' | '\r\n';
  
  // Monitoreo
  watchDirectory: boolean;
  logParsingInterval: number;
  
  // Backup
  enableBackup: boolean;
  backupRetentionDays: number;
}

const defaultDinesatConfig: DinesatConfig = {
  playlistOutputPath: 'C:\\Dinesat\\Playlists',
  audioFilesPath: 'C:\\Audio\\Anuncios',
  logFilePath: 'C:\\Dinesat\\Logs\\playback.log',
  backupPath: 'C:\\Dinesat\\Backup',
  
  supportedFormats: ['M3U', 'TXT', 'XML'],
  defaultFormat: 'M3U',
  
  fileEncoding: 'utf8',
  lineEnding: '\r\n',
  
  watchDirectory: true,
  logParsingInterval: 5000, // 5 segundos
  
  enableBackup: true,
  backupRetentionDays: 30
};
```

### Validación de Archivos de Audio
```typescript
export class AudioFileValidator {
  private supportedExtensions = ['.mp3', '.wav', '.aac', '.ogg'];
  
  async validateAudioFile(filePath: string): Promise<AudioValidationResult> {
    const result: AudioValidationResult = {
      isValid: false,
      errors: [],
      metadata: null
    };
    
    try {
      // Verificar extensión
      const ext = path.extname(filePath).toLowerCase();
      if (!this.supportedExtensions.includes(ext)) {
        result.errors.push(`Extensión no soportada: ${ext}`);
        return result;
      }
      
      // Verificar que el archivo existe
      if (!await fs.pathExists(filePath)) {
        result.errors.push('Archivo no encontrado');
        return result;
      }
      
      // Obtener metadata del audio
      const metadata = await this.getAudioMetadata(filePath);
      result.metadata = metadata;
      
      // Validar duración mínima/máxima
      if (metadata.duration < 5) {
        result.errors.push('Duración muy corta (mínimo 5 segundos)');
      }
      
      if (metadata.duration > 300) {
        result.errors.push('Duración muy larga (máximo 5 minutos)');
      }
      
      result.isValid = result.errors.length === 0;
      return result;
      
    } catch (error) {
      result.errors.push(`Error validando archivo: ${error.message}`);
      return result;
    }
  }
  
  private async getAudioMetadata(filePath: string): Promise<AudioMetadata> {
    // Implementación usando librería como node-ffprobe o similar
    // Retorna duración, bitrate, formato, etc.
    return {
      duration: 30, // segundos
      bitrate: 128, // kbps
      format: 'mp3',
      sampleRate: 44100
    };
  }
}

interface AudioValidationResult {
  isValid: boolean;
  errors: string[];
  metadata: AudioMetadata | null;
}

interface AudioMetadata {
  duration: number;
  bitrate: number;
  format: string;
  sampleRate: number;
}
```
