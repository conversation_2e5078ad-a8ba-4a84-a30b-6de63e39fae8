# Especificaciones de Diseño de Interfaz de Usuario

## Filosofía de Diseño Anti-Genérica

### Principios Fundamentales
1. **Broadcast-First**: Diseño inspirado en equipos de broadcast profesionales
2. **Claridad Visual**: Información crítica siempre visible y destacada
3. **Flujo Natural**: Seguir el proceso mental del operador de medios
4. **Feedback Inmediato**: Confirmación visual de todas las acciones
5. **Accesibilidad**: Usable bajo condiciones de estrés y prisa

### Paleta de Colores Personalizada
```css
:root {
  /* Colores primarios inspirados en broadcast */
  --broadcast-red: #E53E3E;      /* Rojo "ON AIR" */
  --broadcast-green: #38A169;    /* Verde "READY" */
  --broadcast-amber: #D69E2E;    /* Ámbar "WARNING" */
  --broadcast-blue: #3182CE;     /* Azul "INFO" */
  
  /* Grises profesionales */
  --studio-black: #1A202C;      /* Negro estudio */
  --console-gray: #2D3748;      /* Gris consola */
  --panel-gray: #4A5568;       /* Gris panel */
  --text-light: #E2E8F0;       /* Texto claro */
  
  /* Acentos únicos */
  --frequency-purple: #805AD5;   /* Púrpura frecuencia */
  --signal-cyan: #00B5D8;       /* Cian señal */
}
```

## Pantallas Principales

### 1. Dashboard Principal - "Control Room"

#### Layout General
```
┌─────────────────────────────────────────────────────────────┐
│ [LOGO] Sistema de Pautas    [🔴 EN VIVO] [⚡ ESTADO] [👤 USER] │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  │
│  │   📺 TELEVISIÓN  │  │   📻 RADIO      │  │  📊 RESUMEN │  │
│  │                 │  │                 │  │             │  │
│  │ Próximo: 14:30  │  │ Próximo: 14:25  │  │ Hoy: 45 ads │  │
│  │ Cliente ABC     │  │ Cliente XYZ     │  │ Activos: 12 │  │
│  │ [●●●●●○○○] 62%  │  │ [●●●●○○○○] 50%  │  │ Fallos: 0   │  │
│  └─────────────────┘  └─────────────────┘  └─────────────┘  │
│                                                             │
│  ┌─────────────────────────────────────────────────────────┐  │
│  │                    🗓️ TIMELINE HOY                      │  │
│  │ ┌─┬─┬─┬─┬─┬─┬─┬─┬─┬─┬─┬─┬─┬─┬─┬─┬─┬─┬─┬─┬─┬─┬─┬─┐      │  │
│  │ │ │ │ │ │█│ │ │█│ │ │ │█│ │ │ │█│ │ │ │█│ │ │ │ │      │  │
│  │ └─┴─┴─┴─┴─┴─┴─┴─┴─┴─┴─┴─┴─┴─┴─┴─┴─┴─┴─┴─┴─┴─┴─┴─┘      │  │
│  │ 06  08  10  12  14  16  18  20  22  00  02  04  06      │  │
│  └─────────────────────────────────────────────────────────┘  │
│                                                             │
│  ┌─────────────────────────────────────────────────────────┐  │
│  │                  🚨 ALERTAS Y NOTIFICACIONES             │  │
│  │ • Campaña "Verano 2024" vence en 3 días                │  │
│  │ • Conflicto detectado: 15:30 TV - Dos anuncios         │  │
│  └─────────────────────────────────────────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

#### Características Únicas
- **Indicadores de Estado en Tiempo Real**: Colores que cambian según el estado de emisión
- **Timeline Visual**: Representación gráfica de la programación del día
- **Alertas Contextuales**: Notificaciones inteligentes con acciones sugeridas
- **Métricas en Vivo**: Contadores que se actualizan automáticamente

### 2. Gestión de Anuncios - "Media Library"

#### Vista de Tarjetas
```
┌─────────────────────────────────────────────────────────────┐
│ 📁 Biblioteca de Anuncios                    [+ NUEVO ANUNCIO] │
├─────────────────────────────────────────────────────────────┤
│ 🔍 [Buscar...] 🏷️[Todos los clientes ▼] 📅[Todas las fechas ▼] │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│ │ 🎬 [PREVIEW] │ │ 🎬 [PREVIEW] │ │ 🎬 [PREVIEW] │ │ 🎬 [...] │ │
│ │             │ │             │ │             │ │         │ │
│ │ Cliente ABC │ │ Cliente XYZ │ │ Cliente DEF │ │         │ │
│ │ Verano 2024 │ │ Nueva Sede  │ │ Descuentos  │ │         │ │
│ │ 30s │ 📺📻  │ │ 15s │ 📺    │ │ 45s │ 📻    │ │         │ │
│ │ ✅ Activo   │ │ ⏳ Pendiente │ │ ⚠️ Por vencer│ │         │ │
│ └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

#### Características Anti-Genéricas
- **Preview Integrado**: Reproducción directa desde la tarjeta
- **Estados Visuales**: Iconografía clara para cada estado
- **Filtros Inteligentes**: Búsqueda contextual por múltiples criterios
- **Drag & Drop**: Arrastrar directamente al calendario

### 3. Programación - "Scheduler Studio"

#### Vista de Calendario Semanal
```
┌─────────────────────────────────────────────────────────────┐
│ 📅 Programación de Pautas        [Semana] [Mes] [Día]       │
├─────────────────────────────────────────────────────────────┤
│ ← Sem 23, 2024 →                              [HOY] [GUARDAR] │
├─────────────────────────────────────────────────────────────┤
│     │ LUN 03 │ MAR 04 │ MIE 05 │ JUE 06 │ VIE 07 │ SAB 08 │ │
├─────┼────────┼────────┼────────┼────────┼────────┼────────┤ │
│06:00│        │        │        │        │        │        │ │
│     │        │        │        │        │        │        │ │
├─────┼────────┼────────┼────────┼────────┼────────┼────────┤ │
│07:00│ [📺ABC]│        │ [📺ABC]│        │ [📺ABC]│        │ │
│     │ 30s    │        │ 30s    │        │ 30s    │        │ │
├─────┼────────┼────────┼────────┼────────┼────────┼────────┤ │
│08:00│        │ [📻XYZ]│        │ [📻XYZ]│        │        │ │
│     │        │ 15s    │        │ 15s    │        │        │ │
├─────┼────────┼────────┼────────┼────────┼────────┼────────┤ │
│09:00│        │        │        │        │        │        │ │
└─────┴────────┴────────┴────────┴────────┴────────┴────────┘ │
```

#### Funcionalidades Avanzadas
- **Drag & Drop Visual**: Arrastrar anuncios desde la biblioteca
- **Detección de Conflictos**: Alertas automáticas por solapamientos
- **Programación Recurrente**: Wizard para patrones repetitivos
- **Vista Multi-Medio**: Separación clara entre TV y Radio

### 4. Reportes - "Analytics Center"

#### Dashboard de Reportes
```
┌─────────────────────────────────────────────────────────────┐
│ 📊 Centro de Reportes                    [GENERAR REPORTE]   │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│ │ 📈 PROOF OF PLAY│ │ 💰 FACTURACIÓN  │ │ 📋 RESUMEN      │ │
│ │                 │ │                 │ │ CAMPAÑA         │ │
│ │ Por cliente     │ │ Por período     │ │ Por anuncio     │ │
│ │ Por período     │ │ Por cliente     │ │ Rendimiento     │ │
│ │ [GENERAR]       │ │ [GENERAR]       │ │ [GENERAR]       │ │
│ └─────────────────┘ └─────────────────┘ └─────────────────┘ │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │                 📋 REPORTES RECIENTES                   │ │
│ │ • Proof of Play - Cliente ABC - Junio 2024 [📄 PDF]    │ │
│ │ • Resumen Campaña - Verano 2024 [📊 Excel]             │ │
│ │ • Facturación - Mayo 2024 [📄 PDF]                     │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## Componentes UI Personalizados

### 1. Timeline Component
```typescript
interface TimelineProps {
  schedules: Schedule[];
  date: Date;
  mediumType: 'tv' | 'radio' | 'both';
  onScheduleClick: (schedule: Schedule) => void;
  onTimeSlotClick: (time: Date) => void;
}

const Timeline: React.FC<TimelineProps> = ({
  schedules,
  date,
  mediumType,
  onScheduleClick,
  onTimeSlotClick
}) => {
  return (
    <div className="timeline-container">
      <div className="timeline-header">
        {/* Horas del día */}
      </div>
      <div className="timeline-body">
        {/* Bloques de anuncios */}
      </div>
    </div>
  );
};
```

### 2. Status Indicator Component
```typescript
interface StatusIndicatorProps {
  status: 'online' | 'offline' | 'warning' | 'error';
  label: string;
  pulse?: boolean;
}

const StatusIndicator: React.FC<StatusIndicatorProps> = ({
  status,
  label,
  pulse = false
}) => {
  const getStatusColor = () => {
    switch (status) {
      case 'online': return 'var(--broadcast-green)';
      case 'offline': return 'var(--studio-black)';
      case 'warning': return 'var(--broadcast-amber)';
      case 'error': return 'var(--broadcast-red)';
    }
  };

  return (
    <div className={`status-indicator ${pulse ? 'pulse' : ''}`}>
      <div 
        className="status-dot" 
        style={{ backgroundColor: getStatusColor() }}
      />
      <span className="status-label">{label}</span>
    </div>
  );
};
```

### 3. Advertisement Card Component
```typescript
interface AdCardProps {
  advertisement: Advertisement;
  onPreview: (ad: Advertisement) => void;
  onEdit: (ad: Advertisement) => void;
  onSchedule: (ad: Advertisement) => void;
  draggable?: boolean;
}

const AdCard: React.FC<AdCardProps> = ({
  advertisement,
  onPreview,
  onEdit,
  onSchedule,
  draggable = false
}) => {
  return (
    <div 
      className="ad-card"
      draggable={draggable}
      onDragStart={(e) => {
        e.dataTransfer.setData('advertisement', JSON.stringify(advertisement));
      }}
    >
      <div className="ad-preview">
        <button onClick={() => onPreview(advertisement)}>
          🎬 PREVIEW
        </button>
      </div>
      <div className="ad-info">
        <h3>{advertisement.name}</h3>
        <p>{advertisement.client.name}</p>
        <div className="ad-meta">
          <span>{advertisement.duration}s</span>
          <span>{advertisement.mediumTypes.join(' ')}</span>
        </div>
      </div>
      <div className="ad-actions">
        <button onClick={() => onEdit(advertisement)}>✏️</button>
        <button onClick={() => onSchedule(advertisement)}>📅</button>
      </div>
    </div>
  );
};
```

## Responsive Design

### Breakpoints Personalizados
```css
/* Diseñado para estaciones de broadcast */
@media (min-width: 1920px) { /* Monitores de estudio */ }
@media (max-width: 1440px) { /* Laptops de producción */ }
@media (max-width: 1024px) { /* Tablets para operadores móviles */ }
@media (max-width: 768px)  { /* Smartphones para emergencias */ }
```

### Adaptaciones por Dispositivo
- **Desktop**: Vista completa con múltiples paneles
- **Tablet**: Vista de pestañas con navegación simplificada
- **Mobile**: Vista de lista con acciones contextuales

## Accesibilidad y Usabilidad

### Características de Accesibilidad
- **Alto Contraste**: Cumple WCAG 2.1 AA
- **Navegación por Teclado**: Shortcuts para operaciones críticas
- **Screen Reader**: Etiquetas ARIA completas
- **Reducción de Movimiento**: Respeta preferencias del usuario

### Shortcuts de Teclado
```
Ctrl + N     → Nuevo anuncio
Ctrl + S     → Guardar programación
Ctrl + R     → Generar reporte
Ctrl + D     → Dashboard
Ctrl + P     → Programación
Ctrl + L     → Biblioteca
Space        → Play/Pause preview
Esc          → Cancelar acción
```

## Animaciones y Transiciones

### Principios de Animación
- **Funcionales**: Solo animaciones que comunican estado
- **Rápidas**: Máximo 200ms para transiciones
- **Naturales**: Easing curves realistas
- **Opcionales**: Respeta `prefers-reduced-motion`

### Ejemplos de Animaciones
```css
.schedule-item {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.schedule-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.status-indicator.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}
```
